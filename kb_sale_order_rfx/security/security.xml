<!--<?xml version="1.0" encoding="utf-8"?>-->

<!--<odoo>-->
<!--    <data noupdate="0">-->
<!--        <record id="module_category_end" model="ir.module.category">-->
<!--            <field name="name">End of Services Calculator 11</field>-->
<!--            <field name="description">Category For End of Services</field>-->
<!--            <field name="sequence">22</field>-->
<!--        </record>-->

<!--        <record id="group_end_of_administration" model="res.groups">-->
<!--            <field name="name">Administrator</field>-->
<!--            <field name="category_id" ref="kb_end_of_service.module_category_end"/>-->
<!--        </record>-->
<!--    </data >-->
<!--</odoo >-->
