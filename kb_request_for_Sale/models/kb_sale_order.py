from odoo import api, fields, models, _
from logging import getLogger
from odoo.exceptions import ValidationError
import logging
from datetime import date, datetime

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    req_id = fields.Many2one('kb_request_for_sale', string="Request for sale")
    
    kb_req_date = fields.Date(related="req_id.kb_date",string="Date of Sale")

    
    date_order = fields.Datetime(
        string="Order Date",
        required=True, readonly=False, copy=False, states = None,
        help="Creation date of draft/sent orders,\nConfirmation date of confirmed orders.",
        default=fields.Datetime.now)
    
    def get_kb_request_for_sale(self):
        self.ensure_one()
        print(self.env.context)
        return {
            'type': 'ir.actions.act_window',
            'name': 'Sale Order',
            'view_mode': 'tree,form',
            'res_model': 'kb_request_for_sale',
            'domain': [('id', '=', self.req_id.id)],
            'context': "{'create': False}"
        }
        
    req_id_count = fields.Integer(compute='compute_req_id_count')

    def compute_req_id_count(self):
        for record in self:
            record.req_id_count = self.env['kb_request_for_sale'].search_count(
                [('id', '=', self.req_id.id)])


    # =========================== Handle order date in confirm stage SO =========================== 
    def _prepare_confirmation_values(self):
        """ Prepare the sales order confirmation values.

        Note: self can contain multiple records.

        :return: Sales Order confirmation values
        :rtype: dict
        """
        rec = super()._prepare_confirmation_values()
        if self.req_id:
            rec.update({'date_order':self.date_order})
        return rec