<odoo>
    <menuitem id="menu_ssf" parent="base.menu_tests" name="Server-side form"/>

    <record id="action_test_o2m_onchange" model="ir.actions.act_window">
        <field name="name">Test o2m onchange thing</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">test_testing_utilities.parent</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_o2m" parent="menu_ssf"
              name="O2M onchange test thing"
              action="action_test_o2m_onchange"/>

    <record id="action_test_m2o_onchange" model="ir.actions.act_window">
        <field name="name">test_default_and_onchange</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">test_testing_utilities.d</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_m2o" parent="menu_ssf"
              name="test_default_and_onchange"
              action="action_test_m2o_onchange"/>

    <record id="action_test_o2m_count" model="ir.actions.act_window">
        <field name="name">test_change_number_of_lines</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">test_testing_utilities.onchange_count</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_o2m_count" parent="menu_ssf"
              name="test_change_number_of_lines"
              action="action_test_o2m_count"/>

    <record id="o2m_parent" model="ir.ui.view">
        <field name="name">Parent External Form</field>
        <field name="priority">1</field>
        <field name="model">test_testing_utilities.parent</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="value"/>
                        <field name="v"/>
                    </group>
                    <group>
                        <field name="subs"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="o2m_parent_readonly" model="ir.ui.view">
        <field name="name">Parent readonly o2m field</field>
        <field name="model">test_testing_utilities.parent</field>
        <field name="inherit_id" ref="o2m_parent"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='subs']" position="attributes">
                <attribute name="readonly">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="o2m_tree" model="ir.ui.view">
        <field name="name">Sub regular</field>
        <field name="priority">1</field>
        <field name="model">test_testing_utilities.sub</field>
        <field name="arch" type="xml">
            <tree>
                <field name="value"/>
                <field name="v"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="o2m_parent_ed" model="ir.ui.view">
        <field name="name">Parent External Tree</field>
        <field name="model">test_testing_utilities.parent</field>
        <field name="arch" type="xml">
            <form>
                <field name="value"/>
                <field name="v"/>
                <field name="subs" context="{'tree_view_ref': 'test_testing_utilities.editable_external'}"/>
            </form>
        </field>
    </record>
    <record id="editable_external" model="ir.ui.view">
        <field name="name">Sub regular</field>
        <field name="model">test_testing_utilities.sub</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="value"/>
                <field name="v"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="o2m_parent_inline" model="ir.ui.view">
        <field name="name">Parent Inline Tree</field>
        <field name="model">test_testing_utilities.parent</field>
        <field name="arch" type="xml">
            <form>
                <field name="value"/>
                <field name="v"/>
                <field name="subs">
                    <tree editable="bottom">
                        <field name="value"/>
                        <field name="name"/>
                    </tree>
                </field>
            </form>
        </field>
    </record>

    <record id="non_normalized_attrs" model="ir.ui.view">
        <field name="name">Non Normalised Domains</field>
        <field name="model">test_testing_utilities.a</field>
        <field name="arch" type="xml">
            <form>
                <field name="f1"/>
                <field name="f2" attrs="{'readonly': [['f1', '=', '63'], ['f3', '=', 5]]}"/>
                <field name="f3"/>
                <field name="f4"/>
                <field name="f5" attrs="{'readonly': 1}"/>
                <field name="f6" readonly="1" force_save="1"/>
            </form>
        </field>
    </record>

    <record id="m2o_onchange_view" model="ir.ui.view">
        <field name="name">Dynamic Onchange Test View</field>
        <field name="model">test_testing_utilities.onchange_parent</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <notebook>
                        <page>
                            <field name="line_ids" widget="one2many" context="{'line_ids': line_ids}">
                                <tree editable="bottom">
                                    <field name="dummy"/>
                                    <field name="flag" invisible="1" readonly="1"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="m2m_change_view" model="ir.ui.view">
        <field name="name">M2M Change View</field>
        <field name="model">test_testing_utilities.f</field>
        <field name="arch" type="xml">
            <form>
                <field name="m2o"/>
                <field name="m2m" nolabel="1">
                    <tree>
                        <field name="name"/>
                        <field name="m2o_ids" widget="many2many_tags"/>
                    </tree>
                    <form>
                        <field name="name"/>
                        <field name="m2o_ids">
                            <tree>
                                <field name="name"/>
                            </tree>
                        </field>
                    </form>
                </field>
            </form>
        </field>
    </record>

    <record id="o2m_recursive_relation_view" model="ir.ui.view">
        <field name="name">Recursive Relation Test View</field>
        <field name="model">test_testing_utilities.recursive</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <field name="one_to_many_id"/>
                    <field name="many_to_one_ids"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="o2m_modifier" model="ir.ui.view">
        <field name="name">Modifier check</field>
        <field name="model">test_testing_utilities.parent</field>
        <field name="inherit_id" ref="o2m_parent"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="subs" position="inside">
                <form>
                    <field name="value" attrs="{'readonly': [('v', '!=', False)]}"/>
                    <field name="v"/>
                </form>
            </field>
        </field>
    </record>

    <record id="o2m_widget_m2m" model="ir.ui.view">
        <field name="name">Treat o2m as m2m</field>
        <field name="model">test_testing_utilities.parent</field>
        <field name="inherit_id" ref="o2m_parent"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="subs" position="attributes">
                <attribute name="widget">many2many</attribute>
            </field>
        </field>
    </record>

    <record id="attrs_using_m2m" model="ir.ui.view">
        <field name="name">Check attr domain using an m2m value</field>
        <field name="model">test_testing_utilities.e</field>
        <field name="arch" type="xml">
            <form>
                <field name="count" attrs="{'readonly': [('m2m', '=', [])]}"/>
                <field name="m2m"/>
            </form>
        </field>
    </record>
</odoo>
