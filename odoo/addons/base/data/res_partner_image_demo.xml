<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data noupdate="0">

        <record id="base.res_partner_address_1" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_1.jpg"/>
        </record>

        <record id="base.res_partner_address_2" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_2.jpg"/>
        </record>

        <record id="base.res_partner_address_3" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_3.jpg"/>
        </record>

        <record id="base.res_partner_address_4" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_4.jpg"/>
        </record>

        <record id="base.res_partner_address_5" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_5.jpg"/>
        </record>

        <record id="base.res_partner_address_7" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_7.jpg"/>
        </record>

        <record id="base.res_partner_address_10" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_10.jpg"/>
        </record>

        <record id="base.res_partner_address_11" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_11.jpg"/>
        </record>

        <record id="base.res_partner_address_13" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_13.jpg"/>
        </record>

        <record id="base.res_partner_address_14" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_14.jpg"/>
        </record>

        <record id="base.res_partner_address_15" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_15.jpg"/>
        </record>

        <record id="base.res_partner_address_16" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_16.jpg"/>
        </record>

        <record id="base.res_partner_address_17" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_17.jpg"/>
        </record>

        <record id="base.res_partner_address_18" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_18.jpg"/>
        </record>

        <record id="base.res_partner_address_24" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_24.jpg"/>
        </record>

        <record id="base.res_partner_address_25" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_25.jpg"/>
        </record>

        <record id="base.res_partner_address_27" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_27.jpg"/>
        </record>

        <record id="base.res_partner_address_28" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_28.jpg"/>
        </record>

        <record id="base.res_partner_address_30" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_30.jpg"/>
        </record>

        <record id="base.res_partner_address_31" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_31.jpg"/>
        </record>

        <record id="base.res_partner_address_32" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_32.jpg"/>
        </record>

        <record id="base.res_partner_address_33" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_33.jpg"/>
        </record>

        <record id="base.res_partner_address_34" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_address_34.jpg"/>
        </record>

        <record id="base.res_partner_main1" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_main1.jpg"/>
        </record>

        <record id="base.res_partner_main2" model="res.partner">
            <field name="image_1920" type="base64" file="base/static/img/res_partner_main2.jpg"/>
        </record>

    </data>
</odoo>
