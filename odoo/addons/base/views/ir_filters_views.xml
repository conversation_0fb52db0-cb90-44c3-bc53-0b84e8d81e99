<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="ir_filters_view_form" model="ir.ui.view">
            <field name="model">ir.filters</field>
            <field name="arch" type="xml">
                <form string="Filters">
                  <sheet>
                    <group col="4">
                        <field name="name"/>
                        <field name="user_id" string="Available for User"/>
                        <field name="model_id"/>
                        <field name="is_default"/>
                        <field name="action_id"/>
                        <field name="active" widget="boolean_toggle"/>
                    </group>
                    <group>
                        <field name="domain" widget="domain" options="{'model': 'model_id'}"/>
                        <field name="context"/>
                        <field name="sort"/>
                    </group>
                   </sheet>
                </form>
            </field>
        </record>
        <record id="ir_filters_view_tree" model="ir.ui.view">
            <field name="model">ir.filters</field>
            <field name="arch" type="xml">
                <tree string="Filters">
                    <field name="name"/>
                    <field name="model_id"/>
                    <field name="user_id"/>
                    <field name="is_default"/>
                    <field name="action_id"/>
                    <field name="domain" groups="base.group_no_one"/>
                    <field name="context" groups="base.group_no_one"/>
                    <field name="sort" groups="base.group_no_one"/>
                </tree>
            </field>
        </record>
        <record id="ir_filters_view_search" model="ir.ui.view">
            <field name="model">ir.filters</field>
            <field name="arch" type="xml">
                <search string="Filters">
                    <field name="name" string="Filter Name"/>
                    <filter string="User" domain="[('user_id','!=',False)]" name="user" help="Filters visible only for one user"/>
                    <filter string="Shared" domain="[('user_id','=',False)]" name="shared" help="Filters shared with all users"/>
                    <filter string="My filters" domain="[('user_id','=',uid)]" name="my_filters" help="Filters created by myself"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="User" name="user" domain="[]" context="{'group_by':'user_id'}"/>
                        <filter string="Model" name="model" domain="[]" context="{'group_by':'model_id'}"/>
                    </group>
                    <field name="model_id"/>
                    <field name="user_id"/>
                </search>
            </field>
        </record>
        <record id="actions_ir_filters_view" model="ir.actions.act_window">
            <field name="name">User-defined Filters</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">ir.filters</field>
        </record>
        <menuitem parent="base.next_id_2" name="User-defined Filters"
            id="menu_ir_filters" action="actions_ir_filters_view" sequence="5"/>
    </data>
</odoo>
