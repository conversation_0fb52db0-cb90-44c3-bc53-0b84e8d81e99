# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_reminder
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-27 10:20+0000\n"
"PO-Revision-Date: 2022-06-27 10:20+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__active
msgid "Active"
msgstr "نشيط"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__model_field
msgid "Choose the field"
msgstr "اختر الحقل"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__model_name
msgid "Choose the model name"
msgstr "اختر اسم النموذج"

#. module: hr_reminder
#: model_terms:ir.actions.act_window,help:hr_reminder.action_hr_reminder
msgid "Click here to configure new periodic reminder."
msgstr ".انقر هنا لتكوين تذكير دوري جديد"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__company_id
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__company_id
msgid "Company"
msgstr "الشركة"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__create_uid
msgid "Created by"
msgstr "انشأ من قبل"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__create_date
msgid "Created on"
msgstr "تم إنشاؤها على"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__date_to
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__date_to
msgid "End date"
msgstr "تاريخ الانتهاء"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__expiry_date
msgid "Expiry date"
msgstr "تاريخ انتهاء الصلاحية"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__model_field
msgid "Field"
msgstr "حقل"

#. module: hr_reminder
#: model_terms:ir.ui.view,arch_db:hr_reminder.hr_reminder_form_view
msgid "HR Reminder"
msgstr "تذكير الموارد البشرية"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__id
msgid "ID"
msgstr "هوية شخصية"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__model_name
msgid "Model"
msgstr "نموذج"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__days_before
msgid "NUmber of days before the reminder"
msgstr "عدد الأيام قبل التذكير"

#. module: hr_reminder
#: model_terms:ir.ui.view,arch_db:hr_reminder.hr_reminder_tree_view
msgid "Pop-Up Reminder"
msgstr "تذكير منبثق"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__reminder_active
msgid "Reminder Active"
msgstr "تذكير بالموقع"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__expiry_date
msgid "Reminder Expiry Date"
msgstr "تذكير تاريخ انتهاء الصلاحية"

#. module: hr_reminder
#: model_terms:ir.ui.view,arch_db:hr_reminder.hr_reminder_form_view
msgid "Reminder Title..."
msgstr "...عنوان التذكير"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__reminder_active
msgid "Reminder active"
msgstr "التذكير نشط"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__days_before
msgid "Reminder before"
msgstr "تذكير من قبل"

#. module: hr_reminder
#: model:ir.actions.server,name:hr_reminder.ir_cron_scheduler_reminder_action_ir_actions_server
#: model:ir.cron,cron_name:hr_reminder.ir_cron_scheduler_reminder_action
#: model:ir.cron,name:hr_reminder.ir_cron_scheduler_reminder_action
msgid "Reminder scheduler"
msgstr "جدولة تذكير"

#. module: hr_reminder
#. openerp-web
#: code:addons/hr_reminder/static/src/xml/reminder_topbar.xml:0
#: code:addons/hr_reminder/static/src/xml/reminder_topbar.xml:0
#: model:ir.actions.act_window,name:hr_reminder.action_hr_reminder
#: model:ir.ui.menu,name:hr_reminder.hr_reminder_menu
#, python-format
msgid "Reminders"
msgstr "تذكير"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__search_by
msgid "Search By"
msgstr "البحث بواسطة"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__date_set
msgid "Select Date"
msgstr "حدد تاريخ"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__date_set
msgid "Select the reminder set date"
msgstr "حدد تاريخ تعيين التذكير"

#. module: hr_reminder
#: model:ir.model.fields.selection,name:hr_reminder.selection__hr_reminder__search_by__set_date
msgid "Set Date"
msgstr "تحديد التاريخ"

#. module: hr_reminder
#: model:ir.model.fields.selection,name:hr_reminder.selection__hr_reminder__search_by__set_period
msgid "Set Period"
msgstr "ضبط الفترة"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__date_from
msgid "Start Date"
msgstr "تاريخ البدء"

#. module: hr_reminder
#: model:ir.model.fields,help:hr_reminder.field_hr_reminder__date_from
msgid "Start date"
msgstr "تاريخ البدء"

#. module: hr_reminder
#: model:ir.model.fields,field_description:hr_reminder.field_hr_reminder__name
msgid "Title"
msgstr "عنوان"

#. module: hr_reminder
#: model:ir.model.fields.selection,name:hr_reminder.selection__hr_reminder__search_by__today
msgid "Today"
msgstr "اليوم"

#. module: hr_reminder
#: model:ir.model,name:hr_reminder.model_hr_reminder
msgid "hr.reminder"
msgstr "ساعة تذكير"