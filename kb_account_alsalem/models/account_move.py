from odoo import models, fields,api
from datetime import date
from xml.dom import ValidationErr
from datetime import datetime, timedelta
from num2words import num2words


class AccountMove(models.Model):
    _inherit = "account.move"
    
    # Account Fields:
    contract_num = fields.Char(string="Contract Number:")
    remarks_note_from = fields.Date(string="Remarks Note From Date:")
    remarks_note_to = fields.Date(string="Remarks Note To Date:")
    subject = fields.Char(string="Subject:")
    po_number = fields.Char(string="PO Number")
    bus_num = fields.Char(string="Bus Number:")
    delivery_note = fields.Char(string="Delivery Note:")
    years= fields.Char(compute="button_click")
    months= fields.Char(compute="button_click")
    lessRetention= fields.Float(compute="calculat_Less_Retention")
    netTotal= fields.Float(compute="calculat_Net_Total")
    
    def button_click(self):
        for rec in self:
         date_start=rec.invoice_date
         dateyear =  datetime.strptime(str(date_start),'%Y-%m-%d').year
         datemonth =  datetime.strptime(str(date_start),'%Y-%m-%d').month
         self.years = dateyear 
         self.months = datemonth

    def calculat_Less_Retention(self):
        for rec in self:
            if rec.x_ded_withvar:
                less = (rec.amount_untaxed * (self.x_dedication / 100)) * 1.15
            else:
                less = rec.amount_untaxed * (self.x_dedication / 100)
            rec.lessRetention=("%.2f" %less)
    
    def calculat_Net_Total(self):
        for rec in self:
           Net = rec.amount_tax - rec.lessRetention
           Total=rec.amount_untaxed + Net
           rec.netTotal=("%.2f" %Total)
    
    def amount_to_text_custom(self, amount, lang_code):
        # words = num2words.num2words(total_value, lang_code)
        num = round(amount, 2)
        if lang_code == 'ar':
            # num = num + 0.*************
            words = num2words(num, lang=lang_code, to = 'currency',)# currency='USD')
        else:
            words = num2words(num, lang=lang_code, to = 'currency', currency='USD')
            words = words.replace('dollars', 'rials')
            words = words.replace('cents', 'halalas')
            words = words.replace('billion', '')
        return words


class AccountLine(models.Model):
    _inherit = "account.move.line"
    
    # Account Lines:
    num_of_days = fields.Char(string="Number of Days")
    service_date = fields.Char(string="Service Date")
    delivery_date = fields.Char(string="Delivery Date")
    sr_no = fields.Char(string="رقم sr.NO")
    lessRetention = fields.Float(string="Less Retantion")

