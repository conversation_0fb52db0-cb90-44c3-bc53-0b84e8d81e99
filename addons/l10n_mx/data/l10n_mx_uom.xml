<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- UOM Categories -->
    <record id="product_uom_categ_service" model="uom.category">
        <field name="name">Service</field>
    </record>

    <!-- UOM.UOM -->
    <!-- SERVICE -->
    <record id="product_uom_service_unit" model="uom.uom">
        <field name="category_id" ref="product_uom_categ_service"/>
        <field name="name">Service Unit</field>
        <field name="factor" eval="1"/>
        <field name="uom_type">reference</field>
    </record>
    <record id="product_uom_activity" model="uom.uom">
        <field name="category_id" ref="product_uom_categ_service"/>
        <field name="name">Activity</field>
        <field name="factor" eval="1"/>
        <field name="uom_type">smaller</field>
    </record>
    <record id="product_uom_job" model="uom.uom">
        <field name="category_id" ref="product_uom_categ_service"/>
        <field name="name">Job</field>
        <field name="factor" eval="1"/>
        <field name="uom_type">smaller</field>
    </record>
</odoo>
