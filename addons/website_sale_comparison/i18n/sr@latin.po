# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_comparison
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_3
msgid ""
".mp4, .avi, .m4v, H.264, AAC-LC, .mov, MPEG-4, .m4v, .mp4, .mov, Motion JPEG"
" (M-JPEG)"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_13
msgid "1 Year Warranty"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_6
msgid "1.2 megapixels"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_15
msgid "10 hrs"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_4
msgid ""
"3, 4, Protected AAC, MP3, MP3 VBR, AAX and AAX), Apple Lossless, WAV, AAC, "
"HE-AAC, Audible (Formats 2, Audible Enhanced Audio, AIFF"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_10
msgid "3G"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_9
msgid "4G"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_5
msgid "5 megapixels"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_11
msgid "7.9 inch LCD Assistive Touchscreen with 1024 x 768 pixels"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<i class=\"fa fa-exchange\"/> Compare"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart\"/>&amp;nbsp;Add to Cart"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<span class=\"h3\">Suggested alternatives: </span>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_17
msgid "Accelerometer, Three-axis Gyro, Ambient Light Sensor, Digital Compass"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr ""

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_4
msgid "Audio Formats Supported"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_14
msgid "Battery Type"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_18
msgid "Bluetooth"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_1
msgid "Brand"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_id_11272
msgid "Category"
msgstr "Категорија"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_name
msgid "Category Name"
msgstr "Ime kategorije"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:8
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:20
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#, python-format
msgid "Compare"
msgstr ""

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:45
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#, python-format
msgid "Compare Products"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
msgid "Dimensions"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_11
msgid "Display Type"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_2
msgid "Dual Core A5"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_id
msgid "ID"
msgstr "ID"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_12
msgid ""
"LED Backlit Multi-touch Display with IPS Technology, Fingerprint-resistant "
"Oleophobic Coating"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_14
msgid "Lithium - Polymer"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_9
msgid "No"
msgstr "Ne"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_16
msgid "Operating System"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_12
msgid "Other Display Features"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_15
msgid "Play Time"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_5
msgid "Primary Camera"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_2
msgid "Processor"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "Ukloni"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_6
msgid "Secondary Camera"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_17
msgid "Sensors"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute_category_id_11272
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications for"
msgstr ""

#. module: website_sale_comparison
#: code:addons/website_sale_comparison/controllers/main.py:23
#: code:addons/website_sale_comparison/models/website_sale_comparison.py:30
#, python-format
msgid "Uncategorized"
msgstr "Nekategorisano"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_3
msgid "Video Formats Supported"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_13
msgid "Warranty Summary"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "Težina"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_10
msgid "Yes"
msgstr "Da"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:15
#, python-format
msgid "You can compare max 4 products."
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_16
msgid "iOS 6"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr "ili"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_18
msgid "v4"
msgstr ""
