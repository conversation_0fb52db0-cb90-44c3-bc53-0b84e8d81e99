# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_delivery
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:35+0000\n"
"PO-Revision-Date: 2017-10-10 11:35+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:50
#, python-format
msgid ""
"%s does not ship to your address, please choose another one.\n"
"(Error: %s)"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "<i class=\"fa fa-arrow-right\" aria-hidden=\"true\"/> Add delivery methods"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid ""
"<span class=\"badge pull-right o_delivery_compute\">Select to compute "
"delivery rate</span>"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid ""
"<span class=\"col-xs-6 text-right text-muted\" title=\"Delivery will be "
"updated after choosing a new delivery method\"> Delivery:</span>"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_delivery_carrier
msgid "Carrier"
msgstr "Prevoznik"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Choose a delivery method"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_res_country
msgid "Country"
msgstr "Država"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "DHL Delivery Methods"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order_amount_delivery
msgid "Delivery Amount"
msgstr ""

#. module: website_sale_delivery
#: model:ir.ui.menu,name:website_sale_delivery.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Delivery Methods"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description"
msgstr "Opis"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_description
msgid "Description for Online Quotations"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "FedEx Delivery Methods"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Free"
msgstr "Slobodan"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order_has_delivery
msgid "Has an order line set for delivery"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order_has_delivery
msgid "Has delivery"
msgstr ""

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:42
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:49
#, python-format
msgid "Ouch, you cannot choose this carrier!"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_search_inherit_website_sale_delivery
msgid "Published"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order
msgid "Quotation"
msgstr "Ponuda"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:41
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order_amount_delivery
msgid "The amount without tax."
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.portal_order_page_shipping_tracking
msgid "Tracking:"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "USPS Delivery Methods"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_published
msgid "Visible in Website"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "bpost Delivery Methods"
msgstr ""
