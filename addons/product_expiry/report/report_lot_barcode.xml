<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_lot_label_expiry" inherit_id="stock.report_lot_label">
    <xpath expr="//div[@name='lot_name']" position="after">
        <t t-if="o.use_expiration_date">
            <div class="o_label_4x12" t-if="o.use_date">
                B.b. <t t-out="o.use_date" t-options='{"widget": "date"}'/>
            </div>
            <div class="o_label_4x12" t-if="o.expiration_date">
                Exp. <t t-out="o.expiration_date" t-options='{"widget": "date"}'/>
            </div>
        </t>
    </xpath>
    <xpath expr="//t[@name='gs1_datamatrix_lot']" position="before">
        <t t-if="o.use_expiration_date">
            <t t-if="o.use_date" t-set="final_barcode" t-value="(final_barcode or '') + '15' + o.use_date.strftime('%y%m%d')"/>
            <t t-if="o.expiration_date" t-set="final_barcode" t-value="(final_barcode or '') + '17' + o.expiration_date.strftime('%y%m%d')"/>
        </t>
    </xpath>
</template>

<template id="label_lot_template_view_expiry" inherit_id="stock.label_lot_template_view">
    <xpath expr="//t[@name='datamatrix_lot']" position="before">
        <t t-if="lot['lot_record'].use_expiration_date">
            <t t-if="lot['lot_record'].use_date">
                <t t-set="final_barcode" t-value="(final_barcode or '') + '15' + lot['lot_record'].use_date.strftime('%y%m%d')"/>
^FO100,150
^A0N,44,33^FDBest before: <t t-out="lot['lot_record'].use_date" t-options='{"widget": "date"}'/>^FS
            </t>
            <t t-if="lot['lot_record'].expiration_date">
                <t t-set="final_barcode" t-value="(final_barcode or '') + '17' + lot['lot_record'].expiration_date.strftime('%y%m%d')"/>
                    <t t-if="not lot['lot_record'].use_date">
^FO100,150
                    </t>
                    <t t-else="">
^FO100,200
                    </t>
^A0N,44,33^FDUse by: <t t-out="lot['lot_record'].expiration_date" t-options='{"widget": "date"}'/>^FS
            </t>
        </t>
    </xpath>
    <xpath expr="//t[@name='code128_barcode']" position="before">
        <t t-elif="lot['lot_record'].use_expiration_date and (lot['lot_record'].use_date or lot['lot_record'].expiration_date)">
^FO100,50
^A0N,44,33^FD<t t-out="lot['display_name_markup']"/>^FS
^FO100,100
^A0N,44,33^FDLN/SN: <t t-out="lot['name']"/>^FS
            <t t-if="lot['lot_record'].use_date">
^FO100,150
^A0N,44,33^FDBest before: <t t-out="lot['lot_record'].use_date" t-options='{"widget": "date"}'/>^FS
                <t t-if="lot['lot_record'].expiration_date">
^FO100,200
^A0N,44,33^FDUse by: <t t-out="lot['lot_record'].expiration_date" t-options='{"widget": "date"}'/>^FS
^FO100,250^BY3
                </t>
                <t t-else="">
^FO100,200^BY3
                </t>
^BCN,100,Y,N,N
^FD<t t-out="lot['name']"/>^FS
            </t>
            <t t-elif="lot['lot_record'].expiration_date">
^FO100,150
^A0N,44,33^FDUse by: <t t-out="lot['lot_record'].expiration_date" t-options='{"widget": "date"}'/>^FS
^FO100,200^BY3
^BCN,100,Y,N,N
^FD<t t-out="lot['name']"/>^FS
            </t>
        </t>
    </xpath>
</template>
</odoo>
