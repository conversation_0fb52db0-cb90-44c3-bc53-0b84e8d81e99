<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="28" height="2" x="0" y="0"/>
    <filter id="filter-2" width="103.6%" height="200%" x="-1.8%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <rect id="path-3" width="21" height="1" x="0" y="5"/>
    <filter id="filter-4" width="104.8%" height="300%" x="-2.4%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-5" width="24" height="1" x="0" y="8"/>
    <filter id="filter-6" width="104.2%" height="300%" x="-2.1%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-7" width="17" height="1" x="0" y="11"/>
    <filter id="filter-8" width="105.9%" height="300%" x="-2.9%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <linearGradient id="linearGradient-9" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-10" d="M9.758 10.149c0 .527-.15.98-.453 1.357-.302.377-.666.565-1.092.565H2.018c-.426 0-.79-.188-1.092-.565-.302-.378-.453-.83-.453-1.357 0-.411.02-.8.061-1.164.041-.365.118-.733.229-1.103.111-.37.253-.687.424-.95a2.03 2.03 0 0 1 .682-.646c.283-.167.608-.25.976-.25.633.619 1.39.928 2.27.928.88 0 1.638-.31 2.271-.928.368 0 .693.083.976.25.283.167.51.382.682.646.171.263.313.58.424.95.111.37.188.738.229 1.103.04.365.061.753.061 1.164zM7.901 3.714c0 .77-.272 1.426-.816 1.97-.544.544-1.2.816-1.97.816a2.684 2.684 0 0 1-1.97-.816 2.684 2.684 0 0 1-.815-1.97c0-.769.272-1.425.816-1.97A2.684 2.684 0 0 1 5.116.93c.768 0 1.425.272 1.97.816.543.544.815 1.2.815 1.97z"/>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_speaker_bio">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(21 24)">
        <g class="group_2" transform="translate(13 1)">
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".95" xlink:href="#path-1"/>
          </g>
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-5"/>
          </g>
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-7"/>
          </g>
        </g>
        <mask id="mask-11" fill="#fff">
          <use xlink:href="#path-10"/>
        </mask>
        <use fill="url(#linearGradient-9)" class="user" xlink:href="#path-10"/>
      </g>
    </g>
  </g>
</svg>
