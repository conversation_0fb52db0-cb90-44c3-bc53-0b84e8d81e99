LV:
Pēc š<PERSON> moduļa uzstādīšanas tiks izveidoti:
* standarta kontu plāns,
* kontu grupu saraksts,
* PVN analītika,
* Latvijas un Lietuvas banku saraksts.

Lai varētu pilnvērtīgi izmantot šī moduļa iespē<PERSON>, jums būs ne<PERSON> sekojoši moduļi:
* account,
* base_vat,
* l10n_multilang,
* kā arī account_accountant Enterprise versijai vai analogs modulis Community versijai.

PVN konts ir 5721, bet jūs variet to mainīt sadaļā Grāmatvedība -> Nodokļi.
Konta piesaiste bilances un peļņas vai zaudējumu posteņiem tiek veikta, izmantojot “tag_ids/id” sadaļā Kontu plāns – Birkas.

EN:
by installing these modules, will be created:
* Chart of accounts (standardized),
* the list of account groups,
* VAT analytics,
* The list of banks of Latvia and Lithuania.

To get around and make full use of these features, you will need the following modules:
* account,
* base_vat,
* l10n_multilang,
* as well as account_accountant (for Enterprise version) or similar modules for Community version.

The VAT account is created as 5721, but you can modify and change it via Accounting -> Taxes.
Account linking to Balance Sheet items and Profit/Loss items - via "tag_ids/id" in Chart of Accounts - Tags
