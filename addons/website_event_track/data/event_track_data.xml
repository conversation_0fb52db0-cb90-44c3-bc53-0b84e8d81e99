<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="event_track_stage0" model="event.track.stage">
            <field name="name">Proposal</field>
            <field name="sequence">1</field>
            <field name="color">1</field>
        </record>
        <record id="event_track_stage1" model="event.track.stage">
            <field name="name">Confirmed</field>
            <field name="sequence">2</field>
            <field name="mail_template_id" ref="mail_template_data_track_confirmation"/>
            <field name="color">2</field>
        </record>
        <record id="event_track_stage2" model="event.track.stage">
            <field name="name">Announced</field>
            <field name="sequence">3</field>
            <field name="color">3</field>
            <field name="is_visible_in_agenda" eval="True"/>
        </record>
        <record id="event_track_stage3" model="event.track.stage">
            <field name="name">Published</field>
            <field name="sequence">4</field>
            <field name="color">4</field>
            <field name="is_visible_in_agenda" eval="True"/>
            <field name="is_fully_accessible" eval="True"/>
        </record>
        <record id="event_track_stage4" model="event.track.stage">
            <field name="name">Refused</field>
            <field name="sequence">5</field>
            <field name="color">5</field>
            <field name="fold" eval="True"/>
        </record>
        <record id="event_track_stage5" model="event.track.stage">
            <field name="name">Cancelled</field>
            <field name="sequence">6</field>
            <field name="fold" eval="True"/>
            <field name="is_cancel" eval="True"/>
        </record>
    </data>
</odoo>
