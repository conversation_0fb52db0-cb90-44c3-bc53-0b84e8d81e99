<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="23.077" height="18.116" x="0" y="0"/>
    <linearGradient id="linearGradient-3" x1="72.875%" x2="40.332%" y1="46.509%" y2="34.249%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="88.517%" x2="50%" y1="39.469%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-5" width="25" height="2" x="0" y="0"/>
    <filter id="filter-6" width="104%" height="200%" x="-2%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-7" d="M23 12v1H0v-1h23zm-4-3v1H0V9h19zm4-3v1H0V6h23z"/>
    <filter id="filter-8" width="104.3%" height="128.6%" x="-2.2%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_text_image">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 21)">
        <g class="image_1_border" transform="translate(29)">
          <rect width="24" height="19" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.462 .442)">
            <mask id="mask-2" fill="#fff">
              <use xlink:href="#path-1"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
            <ellipse cx="17.769" cy="4.64" fill="#F3EC60" class="oval" mask="url(#mask-2)" rx="3.462" ry="3.314"/>
            <ellipse cx="23.308" cy="19.884" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-2)" rx="10.846" ry="6.628"/>
            <ellipse cx=".231" cy="20.105" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-2)" rx="17.308" ry="10.384"/>
          </g>
          <path fill="#FFF" d="M24 0v19H0V0h24zm-1 1H1v17h22V1z" class="rectangle_2"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-5"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-8)" xlink:href="#path-7"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-7"/>
        </g>
      </g>
    </g>
  </g>
</svg>
