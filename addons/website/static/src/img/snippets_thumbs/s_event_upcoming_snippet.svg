<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="28" height="4" x="0" y="0"/>
    <filter id="filter-2" width="103.6%" height="150%" x="-1.8%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <rect id="path-3" width="28" height="4" x="0" y="5"/>
    <filter id="filter-4" width="103.6%" height="150%" x="-1.8%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <rect id="path-5" width="28" height="4" x="0" y="10"/>
    <filter id="filter-6" width="103.6%" height="150%" x="-1.8%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <linearGradient id="linearGradient-7" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-8" d="M7.429 3.279L9.72 5.571l-4.15 4.15-2.29-2.292 4.15-4.15zm-1.531 7.102l4.483-4.483a.446.446 0 0 0 .138-.327.446.446 0 0 0-.138-.326L7.755 2.619a.443.443 0 0 0-.326-.13.443.443 0 0 0-.327.13L2.62 7.102a.446.446 0 0 0-.138.327c0 .125.046.234.138.326l2.626 2.626c.087.087.196.13.326.13.131 0 .24-.043.327-.13zm6.45-4.621l-6.58 6.587a.893.893 0 0 1-.657.269.893.893 0 0 1-.657-.269l-.914-.914c.271-.27.406-.6.406-.987 0-.386-.135-.715-.406-.986-.27-.271-.6-.406-.986-.406-.387 0-.716.135-.987.406L.66 8.546a.893.893 0 0 1-.268-.657c0-.259.09-.477.268-.656L7.24.66a.893.893 0 0 1 .656-.268c.26 0 .478.09.657.268l.907.907c-.271.27-.406.6-.406.987 0 .386.135.715.406.986.27.271.6.406.986.406.387 0 .716-.135.987-.406l.914.907a.893.893 0 0 1 .269.657c0 .258-.09.477-.269.656z"/>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_country_events">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(18 23)">
        <g class="group_2" transform="translate(18)">
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
            <use fill="#FFF" fill-opacity=".95" xlink:href="#path-1"/>
          </g>
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".95" xlink:href="#path-3"/>
          </g>
          <g class="rectangle">
            <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
            <use fill="#FFF" fill-opacity=".95" xlink:href="#path-5"/>
          </g>
        </g>
        <mask id="mask-9" fill="#fff">
          <use xlink:href="#path-8"/>
        </mask>
        <use fill="url(#linearGradient-7)" class="ticket" xlink:href="#path-8"/>
      </g>
    </g>
  </g>
</svg>
