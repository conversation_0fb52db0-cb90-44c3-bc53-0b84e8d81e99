{% extends "layout.html" %}
{% from "loading.html" import loading_block_ui %}
{% block head %}
    <script>
        $(document).ready(function () {
            $('#wifi-config').submit(function(e){
                e.preventDefault();
                $('.loading-block').removeClass('o_hide');
                $.ajax({
                    url: '/wifi_connect',
                    type: 'post',
                    data: $('#wifi-config').serialize(),
                }).done(function (message) {
                    var data = JSON.parse(message);
                    var message = data.message;
                    if (data.server) {
                        message += '<br>'+ data.server.message;
                        setTimeout(function () {
                            window.location = data.server.url;
                        }, 30000);
                    }
                    $('.message-status').html(message);
                }).fail(function () {
                    $('.error-message').text('Error in connecting to wifi');
                    $('.loading-block').addClass('o_hide');
                });
            });
        });
    </script>
{% endblock %}
{% block content %}
    <h2 class="text-center">Configure Wifi</h2>
    <p>
        Here you can configure how the iotbox should connect to wireless networks.
        Currently only Open and WPA networks are supported. When enabling the persistent checkbox,
        the chosen network will be saved and the iotbox will attempt to connect to it every time it boots.
    </p>
    <form id="wifi-config" action='/wifi_connect' method='POST'>
        <table align="center">
            <tr>
                <td>ESSID</td>
                <td>
                    <select name="essid">
                        {% for id in ssid -%}
                            <option value="{{ id }}">{{ id }}</option>
                        {%- endfor %}
                    </select>
                </td>
            </tr>
            <tr>
                <td>Password</td>
                <td><input type="password" name="password" placeholder="optional"/></td>
            </tr>
            <tr>
                <td>Persistent</td>
                <td><input type="checkbox" name="persistent"/></td>
            </tr>
            <tr>
                <td/>
                <td><input class="btn" type="submit" value="Connect"/></td>
            </tr>
        </table>
    </form>
    <div class="text-center font-small" style="margin: 10px auto;">
        You can clear the persistent configuration
        <form style="display: inline-block;margin-left: 4px;" action='/wifi_clear'>
            <input class="btn btn-sm" type="submit" value="Clear"/>
        </form>
    </div>
    {{ loading_block_ui(loading_message) }}
{% endblock %}
