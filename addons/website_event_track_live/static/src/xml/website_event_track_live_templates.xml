<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

<div t-name="website_event_track_live.website_event_track_replay_suggestion"
    class="owevent_track_suggestion position-absolute w-100"
    t-attf-style="background-image: url('#{widget.currentTrack.imageSrc}');">
    <div class="h-100 w-100 p-4 d-flex flex-column align-items-center owevent_track_suggestion_content">
        <div class="flex-grow-1 pt-5 d-flex flex-column align-items-center justify-content-center">
            <div class="text-white">You just watched:</div>
            <div class="h1 text-white" t-out="widget.currentTrack.name"/>
            <div>
                <a href="#" class="owevent_track_suggestion_replay fw-bold">
                    <span>Replay Video</span>
                </a>
            </div>
        </div>
    </div>
</div>

<div t-name="website_event_track_live.website_event_track_suggestion"
    class="owevent_track_suggestion position-absolute w-100"
    t-attf-style="background-image: url('#{widget.currentTrack.imageSrc}');">
    <div class="h-100 w-100 p-4 d-flex flex-column align-items-center owevent_track_suggestion_content">
        <div class="flex-grow-1 pt-5 d-flex flex-column align-items-center justify-content-center">
            <div class="text-white">You just watched:</div>
            <div class="h1 text-white" t-out="widget.currentTrack.name"/>
            <div>
                <a href="#" class="owevent_track_suggestion_replay fw-bold">
                    <span>Replay Video</span>
                </a>
            </div>
        </div>
        <div class="d-flex w-100 flex-column align-items-end">
            <div class="p-2 rounded owevent_track_suggestion_next position-relative">
                <div class="text-white text-end owevent_track_suggestion_close_wrapper">
                    <i class="fa fa-remove owevent_track_suggestion_close position-absolute"/>
                </div>
                <span class="text-white">Up Next:</span>
                <span class="text-primary fw-bold pe-4" t-out="widget.suggestion.name"/>
                <div class="text-white owevent_track_suggestion_timer_text_wrapper">
                    <span>Starts in</span>
                    <span class="owevent_track_suggestion_timer_text">30</span>
                </div>
            </div>
        </div>
    </div>
</div>

</templates>
