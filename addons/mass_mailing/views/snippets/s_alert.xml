<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="s_alert" name="<PERSON><PERSON>">
    <div class="s_mail_alert o_mail_snippet_general pt16 pb16 mx-auto">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="s_alert s_alert_md s_alert_info w-100" style="background-color: rgb(209 236 241); border-width: 1px !important; border-color: rgb(190 229 235) !important;">
                        <div class="s_alert_icon" valign="top">
                            <i class="fa fa-2x fa-info-circle" style="color: rgb(12 84 96);"/>
                        </div>
                        <div class="s_alert_content">
                            <h3><span style="color: rgb(12 84 96); font-size: 16px; font-weight: bolder;">Explain the benefits you offer</span></h3>
                            <p><font style="color: rgb(12 84 96);">Don't write about products or services here, write about solutions.</font></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<template id="s_alert_options" inherit_id="mass_mailing.snippet_options">
    <!-- Keep those options in separate xpath for options order -->
    <xpath expr="//div[@id='so_width']" position="after">
        <div data-selector=".s_mail_alert .s_alert">
            <we-select string="Size">
                <we-button data-select-class="s_alert_sm">Small</we-button>
                <we-button data-select-class="s_alert_md">Medium</we-button>
                <we-button data-select-class="s_alert_lg">Large</we-button>
            </we-select>
            <we-colorpicker string="Background Color" data-name="alert_colorpicker_opt"
                data-select-style="true"
                data-css-property="background-color"
                data-color-prefix="alert-"/>
        </div>
        <div data-selector=".s_mail_alert .s_alert">
            <t t-call="mass_mailing.snippet_options_border_widgets">
                <t t-set="so_rounded_no_dependencies" t-value="True"/>
            </t>
        </div>
    </xpath>
</template>

<!-- Assets -->
<record id="mass_mailing.s_alert_001_scss" model="ir.asset">
    <field name="name">Alert 001 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">mass_mailing/static/src/snippets/s_alert/000.scss</field>
</record>

</odoo>
