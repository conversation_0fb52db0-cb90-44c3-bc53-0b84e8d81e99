<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="27.778%" x2="72.222%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00E2FF"/>
      <stop offset="100%" stop-color="#00A09D"/>
    </linearGradient>
    <linearGradient id="linearGradient-2" x1="27.778%" x2="72.222%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <linearGradient id="linearGradient-3" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-4" d="M27 50.429V52H13v-1.571h14zm-1.556-4.715v1.572h-9.333v-1.572h9.333zM27 41v1.571H13V41h14z"/>
    <filter id="filter-5" width="107.1%" height="118.2%" x="-3.6%" y="-4.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.2 0"/>
    </filter>
    <rect id="path-6" width="27" height="3" x="7" y="33"/>
    <filter id="filter-7" width="103.7%" height="166.7%" x="-1.9%" y="-16.7%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <path id="path-8" d="M69 50.429V52H55v-1.571h14zm-1.556-4.715v1.572h-9.333v-1.572h9.333zM69 41v1.571H55V41h14z"/>
    <filter id="filter-9" width="107.1%" height="118.2%" x="-3.6%" y="-4.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.2 0"/>
    </filter>
    <rect id="path-10" width="27" height="3" x="49" y="33"/>
    <filter id="filter-11" width="103.7%" height="166.7%" x="-1.9%" y="-16.7%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_color_blocks_2">
      <rect width="82" height="60" class="bg"/>
      <g class="group">
        <path fill="url(#linearGradient-1)" d="M82 0v60H42V0h40zM63 6c-5.523 0-10 4.494-10 10.038 0 5.544 4.477 10.038 10 10.038s10-4.494 10-10.038C73 10.494 68.523 6 63 6z" class="combined_shape" opacity=".4"/>
        <path fill="url(#linearGradient-2)" d="M40 0v60H0V0h40zM20 6c-5.523 0-10 4.494-10 10.038 0 5.544 4.477 10.038 10 10.038s10-4.494 10-10.038C30 10.494 25.523 6 20 6z" class="combined_shape" opacity=".4"/>
        <path fill="url(#linearGradient-3)" d="M20 7a9 9 0 1 1 0 18 9 9 0 0 1 0-18zm43 0a9 9 0 1 1 0 18 9 9 0 0 1 0-18z" class="combined_shape"/>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-5)" xlink:href="#path-4"/>
          <use fill="#FFF" fill-opacity=".8" xlink:href="#path-4"/>
        </g>
        <g class="rectangle_copy">
          <use fill="#000" filter="url(#filter-7)" xlink:href="#path-6"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-6"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-9)" xlink:href="#path-8"/>
          <use fill="#FFF" fill-opacity=".8" xlink:href="#path-8"/>
        </g>
        <g class="rectangle_copy">
          <use fill="#000" filter="url(#filter-11)" xlink:href="#path-10"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-10"/>
        </g>
      </g>
    </g>
  </g>
</svg>
