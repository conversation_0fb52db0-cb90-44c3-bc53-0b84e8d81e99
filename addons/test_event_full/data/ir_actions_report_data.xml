<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="event_registration_report_test" model="ir.actions.report">
        <field name="name">Test Report</field>
        <field name="model">event.registration</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">test_event_full.event_registration_template_report</field>
        <field name="report_file">test_event_full.event_registration_template_report</field>
        <field name="print_report_name">'Badge - %s - %s' % ((object.event_id.name or 'Event').replace('/',''), (object.name or '').replace('/',''))</field>
        <field name="binding_model_id" ref="event.model_event_registration"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
