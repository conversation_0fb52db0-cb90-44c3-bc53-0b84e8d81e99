<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="arabic_english_invoice_with_snln" inherit_id="l10n_gcc_invoice.arabic_english_invoice">
        <xpath expr="//div[@id='total']" position="after">
          <t groups="stock_account.group_lot_on_invoice">
            <t t-set="lot_values" t-value="o._get_invoiced_lot_values()"/>
            <t t-if="lot_values">
                <br/>
                <table class="table table-sm" style="width: 50%;" name="invoice_snln_table">
                    <thead>
                        <tr>
                            <th><span>
                                Product
                                </span>
                                <br/>
                                <span>
                                 المنتج
                                </span>
                            </th>
                            <th class="text-end">
                                <span>
                                    Quantity
                                </span>
                                <br/>
                                <span>
                                    الكمية
                                </span>
                            </th>
                            <th class="text-end">
                                <span>
                                    SN/LN
                                </span>
                                <br/>
                                <span>
                                    رقم مسلسل\لوط
                                </span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="lot_values" t-as="snln_line">
                            <tr>
                                <td><t t-esc="snln_line['product_name']"/></td>
                                <td class="text-end">
                                    <t t-esc="snln_line['quantity']"/>
                                    <t t-esc="snln_line['uom_name']" groups="uom.group_uom"/>
                                </td>
                                <td class="text-end"><t t-esc="snln_line['lot_name']"/></td>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </t>
          </t>
        </xpath>
    </template>
</odoo>
