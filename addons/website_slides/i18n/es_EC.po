# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_slides
#
# Translators:
# <PERSON> <aju<PERSON><PERSON><EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-04 19:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"\n"
"<p>Hello,</p>\n"
"<p>\n"
"    ${user.name} shared the ${object.slide_type} <strong>${object.name}</"
"strong> with you!\n"
"</p>\n"
"<p style=\"text-align: center; margin-top: 10px;\">\n"
"    <a href=\"${object.website_url}\">\n"
"        <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide."
"slide/${object.id}/image\" style=\"height:auto; width:150px; background-"
"color: #cccccc; margin: 16px;\">\n"
"    </a>\n"
"</p>\n"
"<p style=\"text-align: center; margin-top: 10px;\">\n"
"    <a style=\"-webkit-user-select: none; padding: 5px 10px; font-size: "
"12px; line-height: 18px; color: #FFFFFF; border-color:#a24689; text-"
"decoration: none; display: inline-block; margin-bottom: 0px; font-weight: "
"400; text-align: center; vertical-align: middle; cursor: pointer; white-"
"space: nowrap; background-image: none; background-color: #a24689; border: "
"1px solid #a24689; border-radius:3px\" class=\"o_default_snippet_text\" href="
"\"${object.website_url}\">\n"
"        View <strong>${object.name}</strong>\n"
"    </a>\n"
"</p>\n"
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"\n"
"<p>Hello,</p>\n"
"<p>\n"
"    A new ${object.slide_type} <strong>${object.name}</strong> has been "
"published on ${object.channel_id.name} at ${format_tz(object.write_date, "
"tz=user.tz)}\n"
"</p>\n"
"<p style=\"text-align: center; margin-top: 10px;\">\n"
"    <a href=\"${object.website_url}\">\n"
"        <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide."
"slide/${object.id}/image\" style=\"height:auto; width:150px; background-"
"color: #cccccc; margin: 16px;\">\n"
"    </a>\n"
"</p>\n"
"<p style=\"text-align: center; margin-top: 10px;\">\n"
"    <a style=\"-webkit-user-select: none; padding: 5px 10px; font-size: "
"12px; line-height: 18px; color: #FFFFFF; border-color:#a24689; text-"
"decoration: none; display: inline-block; margin-bottom: 0px; font-weight: "
"400; text-align: center; vertical-align: middle; cursor: pointer; white-"
"space: nowrap; background-image: none; background-color: #a24689; border: "
"1px solid #a24689; border-radius:3px\" class=\"o_default_snippet_text\" href="
"\"${object.website_url}\">\n"
"        View <strong>${object.name}</strong>\n"
"    </a>\n"
"</p>\n"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_count_views
msgid "# Views"
msgstr "# Vistas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_embed_views
msgid "# of Embedded Views"
msgstr "# de Vistas Incrustadas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_slide_views
msgid "# of Website Views"
msgstr "# de vistas del sitio web"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "${user.name} shared a ${object.slide_type} with you!"
msgstr "!${user.name} compartió ${object.slide_type} contigo!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_search
msgid ". Please try again with different keywords."
msgstr ". Por favor intenta de nuevo con diferentes palabras clave."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-id="
"\"#slide_email\"><i class=\"fa fa-envelope\"/> Email</b>"
msgstr ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-id="
"\"#slide_email\"><i class=\"fa fa-envelope\"/> Correo electrónico</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-id="
"\"#slide_embed\"><i class=\"fa fa-code\"/> Embed</b>"
msgstr ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-id="
"\"#slide_embed\"><i class=\"fa fa-code\"/> Incrustar</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-id="
"\"#slide_share\"><i class=\"fa fa-share-alt\"/> Share</b>"
msgstr ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-id="
"\"#slide_share\"><i class=\"fa fa-share-alt\"/> Compartir</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "<b>Share:</b>"
msgstr "<b>Compartir:</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-align-justify\"/> Transcript"
msgstr "<i class=\"fa fa-align-justify\"/> Transcripción"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "<i class=\"fa fa-arrow-right\"/> See all"
msgstr "<i class=\"fa fa-arrow-right\"/> Ver todo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> Estadísticas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-circle-o\"/> Website Views"
msgstr "<i class=\"fa fa-circle-o\"/> Vistas del sitio web"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "<i class=\"fa fa-cloud-upload\"/> Upload"
msgstr "<i class=\"fa fa-cloud-upload\"/> Subir"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-code\"/> Embeded Views"
msgstr "<i class=\"fa fa-code\"/> Vistas incrustadas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-comments-o\"/> Comments"
msgstr "<i class=\"fa fa-comments-o\"/> Comentarios"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid ""
"<i class=\"fa fa-envelope-o\"/>\n"
"                        Send Email"
msgstr ""
"<i class=\"fa fa-envelope-o\"/>\n"
"Enviar correo electrónico"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-exclamation-triangle\"/> This"
msgstr "<i class=\"fa fa-exclamation-triangle\"/> Este"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> Sobre"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "<i class=\"fa fa-home\"/> Home"
msgstr "<i class=\"fa fa-home\"/> Inicio"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                                        The social sharing module will be "
"unlocked when a moderator will allow your publication."
msgstr ""
"<i class=\"fa fa-info-circle\"/>\n"
"El módulo de intercambio social se desbloqueará cuando un moderador le "
"permitirá a su publicación."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-play\"/> Total Views"
msgstr "<i class=\"fa fa-play\"/> Vistas totales"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> Compartir"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-spinner fa-spin\"/> Loading ..."
msgstr "<i class=\"fa fa-spinner fa-spin\"/> Cargando ..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-thumbs-down\"/> Dislikes"
msgstr "<i class=\"fa fa-thumbs-down\"/> No me gusta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-thumbs-up\"/> Likes"
msgstr "<i class=\"fa fa-thumbs-up\"/> Me gusta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge\" id=\"facebook-badge\">0</span>\n"
"                                                    <i class=\"fa fa-"
"facebook-square\"/> Facebook"
msgstr ""
"<span class=\"badge\" id=\"facebook-badge\">0</span>\n"
"<i class=\"fa fa-facebook-square\"/> Facebook"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge\" id=\"google-badge\">0</span>\n"
"                                                    <i class=\"fa fa-google-"
"plus-square\"/> Google+"
msgstr ""
"<span class=\"badge\" id=\"google-badge\">0</span>\n"
"<i class=\"fa fa-google-plus-square\"/> Google+"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge\" id=\"linkedin-badge\">0</span>\n"
"                                                    <i class=\"fa fa-"
"linkedin-square\"/> LinkedIn"
msgstr ""
"<span class=\"badge\" id=\"linkedin-badge\">0</span>\n"
"<i class=\"fa fa-linkedin-square\"/> LinkedIn"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge\" id=\"total-share\">0</span>\n"
"                                                    <i class=\"fa fa-share-"
"alt\"/> Social Shares"
msgstr ""
"<span class=\"badge\" id=\"total-share\">0</span>\n"
"<i class=\"fa fa-share-alt\"/> Acciones Sociales"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge\" id=\"twitter-badge\">0</span>\n"
"                                                    <i class=\"fa fa-twitter-"
"square\"/> Twitter"
msgstr ""
"<span class=\"badge\" id=\"twitter-badge\">0</span>\n"
"<i class=\"fa fa-twitter-square\"/> Twitter"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<span class=\"help-block\">Send presentation through email</span>"
msgstr ""
"<span class=\"help-block\">Enviar presentación por correo electrónico</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid ""
"<span class=\"help-block\">Use permanent link to share in social media</span>"
msgstr ""
"<span class=\"help-block\">Usar vinculo permanente para compartir en redes "
"sociales</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<span class=\"text-muted small\">views</span>"
msgstr "<span class=\"text-muted small\">vistas</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "<span class=\"text-muted\">Sort by:</span>"
msgstr "<span class=\"text-muted\">Ordenar por:</span>"

#. module: website_slides
#: sql_constraint:slide.tag:0
msgid "A tag must be unique!"
msgstr "¡Una etiqueta debe ser única!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Actions"
msgstr "Acciones"

#. module: website_slides
#: selection:slide.slide,download_security:0
msgid "Authentified Users Only"
msgstr "Solo usuarios registrados"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_5
msgid "Awesome Timesheet by Odoo"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_can_see
msgid "Can See"
msgstr "Puede ver"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_can_upload
msgid "Can Upload"
msgstr "Puede subir"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_ir_slide_category
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_category_ids
#: model:ir.ui.menu,name:website_slides.menu_action_ir_slide_category
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Categories"
msgstr "Categorías"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:251
#: code:addons/website_slides/static/src/xml/website_slides.xml:54
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_category_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slides_category_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slides_category_tree
#, python-format
msgid "Category"
msgstr "Categoría"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Channel"
msgstr "Canal"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_group_ids
msgid "Channel Groups"
msgstr "Canales de Grupo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "Channel Settings"
msgstr "Configuración de canal"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:171
#, python-format
msgid "Channel contains the given title, please change before Save or Publish."
msgstr ""
"El canal contiene el título dado, por favor cambielo antes de Guardar o "
"Publicar."

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
msgid "Channel for Slides"
msgstr "Canal para presentaciones"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channel visibility"
msgstr "Visivilidad del canal"

#. module: website_slides
#: model:ir.actions.act_url,name:website_slides.action_open_channels
#: model:ir.actions.act_window,name:website_slides.action_slide_channels
#: model:ir.ui.menu,name:website_slides.menu_action_slide_channels
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "Canales"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_datas
msgid "Content"
msgstr "Contenido"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:20
#, python-format
msgid "Content Preview"
msgstr "Vista previa de contenido"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:289
#, fuzzy, python-format
msgid ""
"Could not fetch data from url. Document or access right not available.\n"
"Here is the received response: %s"
msgstr ""
"No se pudo obtener datos desde la URL. El documento o el acceso no está "
"disponible.\n"
"La respuesta recibida es: %serror"

#. module: website_slides
#: code:addons/website_slides/models/slides.py:294
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available:\n"
"%s"
msgstr ""
"No se pudo obtener datos desde la URL. El documento o el acceso no está "
"disponible:\n"
"%s"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_1
msgid ""
"Create a beautiful and professional eCommerce on your website with Odoo. \n"
"\n"
"Find out more at https://www.odoo.com/page/e-commerce"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:220
#, python-format
msgid "Create new tag '%s'"
msgstr "Crear nueva etiqueta %s'"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.channel_public
msgid ""
"Default channel for slides, all public users can access content of this "
"channel."
msgstr ""
"Canal por defecto de presentaciones, todas los usuarios públicos pueden "
"acceder al contenido de este canal."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:60
#: code:addons/website_slides/static/src/xml/website_slides.xml:62
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_description
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Description"
msgstr "Descripción"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:72
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_2
msgid ""
"Discover the all-in-one business management software solution that fits any "
"business size and use case.\n"
"\n"
"Learn more at https://odoo.com"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_dislikes
msgid "Dislikes"
msgstr "No me gusta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_category_sequence
#: model:ir.model.fields,help:website_slides.field_slide_channel_sequence
msgid "Display order"
msgstr "Orden de visualización"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: selection:slide.slide,slide_type:0
msgid "Document"
msgstr "Documento"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_document_id
msgid "Document ID"
msgstr "ID del documento"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_url
msgid "Document URL"
msgstr "URL del documento"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Documents"
msgstr "Documentos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "Descargar"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_5
msgid ""
"Download Awesome Timesheet by Odoo and discover how easy time tracking and "
"employee management can be!\n"
"\n"
"Chrome: http://bit.ly/2613LcY\n"
"iOS: http://bit.ly/1ZUZsZD"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_download_security
msgid "Download Security"
msgstr "Descargar con seguridad"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Email Address"
msgstr "Dirección de correo electrónico"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_publish_template_id
msgid "Email template to send slide publication through email"
msgstr ""
"Plantilla de correo electrónico para enviar una publicación a travez de un "
"correo electrónico"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_share_template_id
msgid "Email template used when sharing a slide"
msgstr "Plantilla de correo electrónico usada para compartir una presentación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_embed_code
msgid "Embed Code"
msgstr "Código incrustado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_embedcount_ids
msgid "Embed Count"
msgstr "Contador de Incrustados"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in your website"
msgstr "Incrustar en tu sitio web"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "Contador de vistas de presentaciones incrustadas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Embeds"
msgstr "Incrusta"

#. module: website_slides
#: model:slide.channel,name:website_slides.channel_private
msgid "Employee Channel"
msgstr "Canal de empleados"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_access_error_msg
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Error Message"
msgstr "Mensaje de error"

#. module: website_slides
#: selection:slide.slide,download_security:0
msgid "Everyone"
msgstr "Todos"

#. module: website_slides
#: selection:slide.channel,promote_strategy:0
msgid "Featured Presentation"
msgstr "Presentación destacada"

#. module: website_slides
#: model:slide.category,name:website_slides.category_1
msgid "Featured Presentations"
msgstr "Presentaciones destacadas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_promoted_slide_id
msgid "Featured Slide"
msgstr "Diapositiva destacada"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_promote_strategy
msgid "Featuring Policy"
msgstr "Política de destacar"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/controllers/main.py:299
#: code:addons/website_slides/static/src/js/slides_upload.js:90
#, python-format
msgid "File is too big. File size cannot exceed 15MB"
msgstr "El archivo es demasiado grande. El tamaño no puede exceder los 15MB"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_can_see_full
msgid "Full Access"
msgstr "Acceso completo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "General"
msgstr "General"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_website_config_settings_website_slide_google_app_key
msgid "Google Doc Key"
msgstr "Llave de Google Doc"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_graph
msgid "Graph of Slides"
msgstr "Gráficos de Diapositivas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_group_ids
msgid "Groups allowed to see presentations in this channel"
msgstr "Grupos permitidos para ver la presentación en este canal"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_upload_group_ids
msgid ""
"Groups allowed to upload presentations in this channel. If void, every user "
"can upload."
msgstr ""
"Los grupos que se les permite subir presentaciones en este canal. Si no "
"existe, todos los usuarios puede cargarlas."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag_id
msgid "ID"
msgstr "ID (identificación)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_image
msgid "Image"
msgstr "Imagen"

#. module: website_slides
#: selection:slide.slide,slide_type:0
msgid "Infographic"
msgstr "Infografía"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Infographics"
msgstr "Infografías"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:321
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"Error interno del servidor, por favor intente de nuevo más tarde o contacte "
"al administrador del sitio.\n"
"El mensaje de error es:%s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:85
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr ""
"Tipo de archivo inválido. Por favor, seleccione un pdf o archivo de imagen"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category___last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel___last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_embed___last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide___last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_tag___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website_slides
#: selection:slide.channel,promote_strategy:0
msgid "Latest Published"
msgstr "Ultimas publicaciones"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_likes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Likes"
msgstr "Me Gustan"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_image_medium
msgid "Medium"
msgstr "Media"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_access_error_msg
msgid "Message to display when not accessible due to access rights"
msgstr ""
"Mensaje a mostrar cuando no es accesible debido a restricciones de acceso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_mime_type
msgid "Mime-type"
msgstr "Tipo Mime"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#: selection:slide.channel,promote_strategy:0
msgid "Most Viewed"
msgstr "Más vistos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#: selection:slide.channel,promote_strategy:0
msgid "Most Voted"
msgstr "Más votado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag_name
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Name"
msgstr "Nombre"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid "New ${object.slide_type} published on ${object.channel_id.name}"
msgstr "Nuevo ${object.slide_type} publicado en ${object.channel_id.name}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Newest"
msgstr "Más moderno"

#. module: website_slides
#: selection:slide.channel,promote_strategy:0
msgid "No Featured Presentation"
msgstr "Sin presentación destacada"

#. module: website_slides
#: selection:slide.slide,download_security:0
msgid "No One"
msgstr "Ninguno"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channel_not_found
msgid "No channel created or published yet."
msgstr "No hay canales creados o publicados todavía."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "No presentation available."
msgstr "Sin presentación disponible"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "No presentation published yet."
msgstr "Sin presentaciones publicadas aún."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_grid_view
msgid "Not Published"
msgstr "No publicado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_nbr_documents
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_nbr_documents
msgid "Number of Documents"
msgstr "Número de Documentos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_nbr_infographics
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_nbr_infographics
msgid "Number of Infographics"
msgstr "Número de infografías"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_nbr_presentations
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_nbr_presentations
msgid "Number of Presentations"
msgstr "Número de Presentaciones"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_nbr_videos
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_nbr_videos
msgid "Number of Videos"
msgstr "Número de videos"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_2
msgid "Odoo All-in-One Software Demonstration"
msgstr ""

#. module: website_slides
#: model:slide.category,name:website_slides.category_2
msgid "Odoo Days"
msgstr "Días Odoo"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_0
msgid "Odoo Explained"
msgstr "Odoo explicado"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_6
msgid "Odoo Marketing - part 1: ATTRACT"
msgstr "Odoo Marketing - parte 1: ATRAER"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_7
msgid "Odoo Marketing - part 1: ATTRACT_2"
msgstr "Odoo Marketing - parte 1: ATTRACT_2"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_8
msgid "Odoo Marketing - part 1: ATTRACT_3"
msgstr "Odoo Marketing - parte 1: ATTRACT_3"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_10
msgid "Odoo Roadmap & Strategy - Fabien Pinckaers"
msgstr "Mapa de Desarrollo & Estrategia Odoo - Fabien Pinckaers"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_3
msgid "Odoo Sign Demonstration"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_1
msgid "Odoo Website & E-commerce Demonstration"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_6
#, fuzzy
msgid ""
"Odoo's fully-integrated marketing app will help you to attract, convert and "
"close new leads. Check out our three-part video and discover more at www."
"odoo.com"
msgstr ""
"La aplicación de marketing totalmente integrada de Odoo que le ayudará a "
"atraer, convertir y obtener a sus clientes potenciales. Revise nuestro vídeo "
"de tres partes y descubra más en www.odoo.com. \n"
"\n"
"Vídeo realizado por Miroslava Ganzarcikova y Sophia Eribo."

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_7
#: model:slide.slide,description:website_slides.slide_8
msgid ""
"Odoo's fully-integrated marketing app will help you to attract, convert and "
"close new leads. Check out our three-part video and discover more at www."
"odoo.com\n"
"\n"
"Video made by Miroslava Ganzarcikova and Sophia Eribo."
msgstr ""
"La aplicación de marketing totalmente integrada de Odoo que le ayudará a "
"atraer, convertir y obtener a sus clientes potenciales. Revise nuestro vídeo "
"de tres partes y descubra más en www.odoo.com. \n"
"\n"
"Vídeo realizado por Miroslava Ganzarcikova y Sophia Eribo."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_9
msgid "Open Days 2014 Infographic"
msgstr "Infografía Open Days 2014 "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_11
msgid "OpenSource CMS, A performance comparison - Mantavya Gajjar"
msgstr "OpenSource CMS, Una comparación de rendimiento - Mantavya Gajjar"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:28
#, python-format
msgid "PDF or Image File"
msgstr "Archivo de Imagen o PDF"

#. module: website_slides
#: model:slide.channel,name:website_slides.channel_partial
msgid "Partner Channel"
msgstr "Canal de Aliados "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please"
msgstr "Por favor"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides.js:55
#, python-format
msgid "Please <a href=\"/web?redirect=%s\">login</a> to vote this slide"
msgstr ""
"Por favor <a href=\"/web?redirect=%s\">accese</a> para votar por esta "
"presentación"

#. module: website_slides
#: code:addons/website_slides/models/slides.py:297
#: code:addons/website_slides/models/slides.py:518
#, python-format
msgid "Please enter valid Youtube or Google Doc URL"
msgstr "Por favor, ingrese una URL válida de Youtube o Google Doc"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:281
#, python-format
msgid "Please enter valid youtube or google doc url"
msgstr "Por favor, ingrese una URL válida de Youtube o Google Doc"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Post"
msgstr "Entrega"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"Post comment with email address (confirmation of email is required in order "
"to publish comment on website) or please"
msgstr ""
"Publique un comentario con su dirección de correo electrónico (se requiere "
"confirmación de correo electrónico con el fin de publicar comentarios en la "
"página web) o por favor"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_slide_id
#: selection:slide.slide,slide_type:0
msgid "Presentation"
msgstr "Presentación"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "Presentación publicada"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_footer
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#: model:website.menu,name:website_slides.website_menu_slides
msgid "Presentations"
msgstr "Presentaciones"

#. module: website_slides
#: selection:slide.channel,visibility:0
msgid "Private"
msgstr "Privado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channels
msgid "Private channel"
msgstr "Canal privado"

#. module: website_slides
#: selection:slide.channel,visibility:0
msgid "Public"
msgstr "Público"

#. module: website_slides
#: model:slide.channel,name:website_slides.channel_public
msgid "Public Channel"
msgstr "Canal público"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_date_published
msgid "Publish Date"
msgstr "Fecha de Publicación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "Publicado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_publish_template_id
msgid "Published Template"
msgstr "Plantilla de Publicación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Related"
msgstr "Relacionados"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "Results for"
msgstr "Resultados para"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid ""
"Review your channel settings to promote your most viewed or recent published "
"presentation"
msgstr ""
"Revisar la configuración de canal para promover su presentación publicada "
"más reciente o más vistas."

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_0
msgid ""
"Run your business with a comprehensive suite of business apps. Do it easily "
"with Odoo! \n"
"\n"
"http://odoo.com/start"
msgstr ""
"Ejecutar su negocio con un conjunto completo de aplicaciones de negocios. "
"¡Lo puede hacer fácilmente con Odoo!\n"
"\n"
"http://odoo.com/start"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:70
#, python-format
msgid "Save and Publish"
msgstr "Grabar y Publicar"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:69
#, python-format
msgid "Save as Draft"
msgstr "Grabar como Borrador"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Slides"
msgstr "Buscar Diapositivas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Security"
msgstr "Seguridad"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channels
msgid "Select a Channel"
msgstr "Seleccionar un canal"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Select page to start with"
msgstr "Selecciona una página para iniciar con"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid "Share Link"
msgstr "Compartir enlace"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Share count"
msgstr "Compartir conteo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid "Share on Social Networks"
msgstr "Compartir en Redes Sociales"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Share with a friend"
msgstr "Compartir con un amigo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_share_template_id
msgid "Shared Template"
msgstr "Compartir Plantilla"

#. module: website_slides
#: selection:slide.channel,visibility:0
msgid "Show channel but restrict presentations"
msgstr "Mostrar canal pero restringir presentaciones"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Slide"
msgstr "Presentación"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "Etiqueta de presentación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_custom_slide_id
msgid "Slide to Promote"
msgstr "Slide a promover"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slides_slides
#: model:ir.model,name:website_slides.model_slide_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_category_slide_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_slide_ids
#: model:ir.ui.menu,name:website_slides.menu_website_slides_root
#: model:ir.ui.menu,name:website_slides.submenu_action_slides_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Slides"
msgstr "Presentaciones"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_category
msgid "Slides Category"
msgstr "Categoría de Presentación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_search
msgid "Sorry, but nothing matches your search criteria"
msgstr "Lo sentimos, pero nada coincide con su criterio de búsqueda"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_3
msgid ""
"Stop doing manual printing and scanning. Move to electronic signature with "
"Odoo Sign!\n"
"\n"
"Discover more at https://www.odoo.com/page/sign"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
msgid "Tag"
msgstr "Etiqueta"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:274
#: code:addons/website_slides/static/src/xml/website_slides.xml:48
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_tag_ids
#: model:ir.ui.menu,name:website_slides.menu_slide_tag
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#, python-format
msgid "Tags"
msgstr "Etiquetas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "Etiquetas..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "The"
msgstr "El"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide_slide_type
#, fuzzy
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr ""
"Tipo de documento se ajusta automáticamente en función del tipo de archivo, "
"la altura y la anchura."

#. module: website_slides
#: sql_constraint:slide.slide:0
msgid "The slide name must be unique within a channel"
msgstr "El nombre de la presentación debe ser único en este canal"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed_url
msgid "Third Party Website URL"
msgstr "URL de Terceros"

#. module: website_slides
#: model_terms:slide.channel,access_error_msg:website_slides.channel_partial
#: model_terms:slide.channel,access_error_msg:website_slides.channel_private
#: model_terms:slide.channel,access_error_msg:website_slides.channel_public
msgid "This channel is private and its content is restricted to some users."
msgstr "Este canal es privado y su contenido se limita a algunos usuarios."

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:285
#, python-format
msgid ""
"This video already exists in this channel <a target=\"_blank\" href=\"/"
"slides/slide/%s\">click here to view it </a>"
msgstr ""
"Este vídeo existe en este canal <a target=\"_blank\" href=\"/slides/slide/%s"
"\">presione aquí para verlo </a>"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_image_thumb
msgid "Thumbnail"
msgstr "Miniatura"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:42
#: code:addons/website_slides/static/src/xml/website_slides.xml:44
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_name
#, python-format
msgid "Title"
msgstr "Título"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category_total
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_total
msgid "Total"
msgstr "Total"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_total_views
msgid "Total # Views"
msgstr "# Total de Vistas "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_index_content
msgid "Transcript"
msgstr "Transcripción"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_slide_type
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "Tipo"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:140
#, python-format
msgid "Uncategorized"
msgstr "Sin categoría"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "Uncategorized presentation"
msgstr "Presentación sin categorizar"

#. module: website_slides
#: code:addons/website_slides/models/slides.py:507
#, python-format
msgid "Unknown document"
msgstr "Documento desconocido"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_upload_group_ids
msgid "Upload Groups"
msgstr "Subir grupos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid ""
"Upload PDF presentations, documents, videos or infographic using the button "
"below."
msgstr ""
"Sube PDF presentaciones, documentos, vídeos o infografías usando el botón de "
"abajo."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:9
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#, python-format
msgid "Upload Presentation"
msgstr "Cargar Presentación"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:12
#, python-format
msgid "Uploading presentation..."
msgstr "Cargando presentación..."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.channel_private
msgid "Used to publish internal slides of company."
msgstr "Se utiliza para publicar presentaciones internas de la compañía."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.channel_partial
msgid "Used to publish slides in partner network privately."
msgstr "Se utiliza para publicar sus presentaciones en una red privada."

#. module: website_slides
#: selection:slide.slide,slide_type:0
msgid "Video"
msgstr "Video"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Videos"
msgstr "Videos"

#. module: website_slides
#: code:addons/website_slides/models/slides.py:436
#, fuzzy, python-format
msgid "View Slide"
msgstr "Presentaciones Web"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
#: model_terms:ir.ui.view,arch_db:website_slides.slides_grid_view
msgid "Views"
msgstr "Vistas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.related_slides
msgid "Views ."
msgstr "Vistas."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_visibility
msgid "Visibility"
msgstr "Visibilidad"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "Esperando por validación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Website"
msgstr "Sitio web"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_website_config_settings
msgid "Website Slides"
msgstr "Presentaciones Web"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide_website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Write a comment..."
msgstr "Escriba un comentario..."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:115
#, python-format
msgid "You can not upload password protected file."
msgstr "No puede subir un archivo protegido con contraseña."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides.js:64
#, python-format
msgid "You have already voted for this slide"
msgstr "Usted ya ha votado por esta presentación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Your Name"
msgstr "Su nombre"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:36
#, python-format
msgid "Youtube Video URL"
msgstr "URL vídeo de Youtube"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:34
#, python-format
msgid "Youtube or Google Doc URL"
msgstr "URL Youtube o Google Doc "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide_document_id
msgid "Youtube or Google Document ID"
msgstr " ID Youtube o Google Document"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide_url
msgid "Youtube or Google Document URL"
msgstr "URL Youtube o Google Document "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "by email!"
msgstr "por correo electrónico"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "is empty."
msgstr "está vacío."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "is private"
msgstr "es privado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid "is private."
msgstr "es privado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "login"
msgstr "iniciar sesión"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "on"
msgstr "en"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:70
#, python-format
msgid "or"
msgstr "o"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_search
msgid "results found for the given criteria"
msgstr "resultados encontrados de acuerdo al criterio"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "suggest_slide.name"
msgstr "suggest_slide.name"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "to post comment"
msgstr "para publicar un comentario"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "to send this"
msgstr "para enviar esto"

#. module: website_slides
#: model:ir.model,name:website_slides.model_website_config_settings
msgid "website.config.settings"
msgstr "website.config.settings"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<EMAIL>"
msgstr "<EMAIL>"

#~ msgid ""
#~ "\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px; background-color:#ffffff\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center\" valign=\"center\">\n"
#~ "            <a href=\"${user.company_id.website}\" style=\"text-"
#~ "decoration:none\">\n"
#~ "                <img alt=\"Odoo\" src=\"${ctx['base_url']}/logo.png\" "
#~ "style=\"display:block; border:none; min-height:60px; margin:0 auto;\">\n"
#~ "            </a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center; color: #ffffff; "
#~ "background-color:#a24689;\" valign=\"center\">\n"
#~ "            <h2 style=\"margin:0px;\">\n"
#~ "                ${user.name} shared the ${object.slide_type} ${object."
#~ "name} with you!\n"
#~ "            </h2>\n"
#~ "        </td>\n"
#~ "    </tr><tr>\n"
#~ "        <td style=\"width: 600px; vertical-align:top; text-align:center; "
#~ "background-color:#FFFFF; color:#414141\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color: #a24689;\">\n"
#~ "                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/"
#~ "image/slide.slide/${object.id}/image\"\n"
#~ "                    style=\"height:auto; width:500px; background-color: "
#~ "#cccccc; margin: 16px;\">\n"
#~ "            </a>\n"
#~ "            <p style=\"font-size:24px; font-weight:bold\"><a href="
#~ "\"${object.website_url}\">${object.name}</a></p>\n"
#~ "            <p style=\"color: #ccc;\">Published on ${object.write_date}</"
#~ "p>\n"
#~ "            <p><a href=\"${object.website_url}\">Click here to open the "
#~ "${object.slide_type}</a></p>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"margin: 10px 0px 0px "
#~ "0px; vertical-align: top;padding: 0px; font-family:arial; font-size:12px; "
#~ "color: #ffffff; background-color:#8f8f8f;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; font-size:12px; text-align:center; "
#~ "padding-top:10px; color:#a24689; padding-bottom:5px\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color:"
#~ "#a24689\">View in browser</a> |\n"
#~ "            <a href=\"/page/contactus\" style=\"color:#a24689\">Contact</"
#~ "a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>"
#~ msgstr ""
#~ "\n"
#~ "<div style=\"padding:0px; margin:0px;\">Click here to open the\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px; background-color:#ffffff\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center\" valign=\"center\">\n"
#~ "            <a href=\"${user.company_id.website}\" style=\"text-"
#~ "decoration:none\">\n"
#~ "                <img alt=\"Odoo\" src=\"${ctx['base_url']}/logo.png\" "
#~ "style=\"display:block; border:none; min-height:60px; margin:0 auto;\">\n"
#~ "            </a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center; color: #ffffff; "
#~ "background-color:#a24689;\" valign=\"center\">\n"
#~ "            <h2 style=\"margin:0px;\">\n"
#~ "                ¡${user.name} compartió ${object.slide_type} ${object."
#~ "name} con usted!\n"
#~ "            </h2>\n"
#~ "        </td>\n"
#~ "    </tr><tr>\n"
#~ "        <td style=\"width: 600px; vertical-align:top; text-align:center; "
#~ "background-color:#FFFFF; color:#414141\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color: #a24689;\">\n"
#~ "                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/"
#~ "image/slide.slide/${object.id}/image\"\n"
#~ "                    style=\"height:auto; width:500px; background-color: "
#~ "#cccccc; margin: 16px;\">\n"
#~ "            </a>\n"
#~ "            <p style=\"font-size:24px; font-weight:bold\"><a href="
#~ "\"${object.website_url}\">${object.name}</a></p>\n"
#~ "            <p style=\"color: #ccc;\">Publicado el ${object.write_date}</"
#~ "p>\n"
#~ "            <p><a href=\"${object.website_url}\">Presione aquí ${object."
#~ "slide_type} para abrir</a></p>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"margin: 10px 0px 0px "
#~ "0px; vertical-align: top;padding: 0px; font-family:arial; font-size:12px; "
#~ "color: #ffffff; background-color:#8f8f8f;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; font-size:12px; text-align:center; "
#~ "padding-top:10px; color:#a24689; padding-bottom:5px\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color:#a24689\">Ver "
#~ "en el navegador</a> |\n"
#~ "            <a href=\"/page/contactus\" style=\"color:#a24689\">Contacto</"
#~ "a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>"

#~ msgid ""
#~ "\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px; background-color:#ffffff\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center\" valign=\"center\">\n"
#~ "            <a href=\"${user.company_id.website}\" style=\"text-"
#~ "decoration:none\">\n"
#~ "                <img alt=\"Odoo\" src=\"${ctx['base_url']}/logo.png\" "
#~ "style=\"display:block; border:none; min-height:60px; margin:0 auto;\">\n"
#~ "            </a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center; color: #ffffff; "
#~ "background-color:#a24689;\" valign=\"center\">\n"
#~ "            <h2 style=\"margin:0px;\">\n"
#~ "                New ${object.slide_type} published on ${object.channel_id."
#~ "name}\n"
#~ "            </h2>\n"
#~ "        </td>\n"
#~ "    </tr><tr>\n"
#~ "        <td style=\"width: 600px; vertical-align:top; text-align:center; "
#~ "background-color:#FFFFF; color:#414141\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color: #a24689;\">\n"
#~ "                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/"
#~ "image/slide.slide/${object.id}/image\" style=\"height:auto; width:500px; "
#~ "background-color: #cccccc; margin: 16px;\">\n"
#~ "            </a>\n"
#~ "            <p style=\"font-size:24px; font-weight:bold\"><a href="
#~ "\"${object.website_url}\">${object.name}</a></p>\n"
#~ "            <p style=\"color: #ccc;\">Published on ${object.write_date}</"
#~ "p>\n"
#~ "            <p><a href=\"${object.website_url}\">Click here to open the "
#~ "${object.slide_type}</a></p>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"margin: 10px 0px 0px "
#~ "0px; vertical-align: top;padding: 0px; font-family:arial; font-size:12px; "
#~ "color: #ffffff; background-color:#8f8f8f;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; font-size:12px; text-align:center; "
#~ "padding-top:10px; color:#a24689; padding-bottom:5px\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color:"
#~ "#a24689\">View in browser</a> |\n"
#~ "            <a href=\"/page/contactus\" style=\"color:#a24689\">Contact</"
#~ "a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>"
#~ msgstr ""
#~ "\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px; background-color:#ffffff\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center\" valign=\"center\">\n"
#~ "            <a href=\"${user.company_id.website}\" style=\"text-"
#~ "decoration:none\">\n"
#~ "                <img alt=\"Odoo\" src=\"${ctx['base_url']}/logo.png\" "
#~ "style=\"display:block; border:none; min-height:60px; margin:0 auto;\">\n"
#~ "            </a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"width: 600px; margin: "
#~ "10px 0px 0px 0px; vertical-align: top; padding: 0px; font-family:arial; "
#~ "font-size:12px;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; text-align:center; color: #ffffff; "
#~ "background-color:#a24689;\" valign=\"center\">\n"
#~ "            <h2 style=\"margin:0px;\">\n"
#~ "                Un nuevo ${object.slide_type} fue publicado en ${object."
#~ "channel_id.name}\n"
#~ "            </h2>\n"
#~ "        </td>\n"
#~ "    </tr><tr>\n"
#~ "        <td style=\"width: 600px; vertical-align:top; text-align:center; "
#~ "background-color:#FFFFF; color:#414141\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color: #a24689;\">\n"
#~ "                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/"
#~ "image/slide.slide/${object.id}/image\" style=\"height:auto; width:500px; "
#~ "background-color: #cccccc; margin: 16px;\">\n"
#~ "            </a>\n"
#~ "            <p style=\"font-size:24px; font-weight:bold\"><a href="
#~ "\"${object.website_url}\">${object.name}</a></p>\n"
#~ "            <p style=\"color: #ccc;\">Publicado el ${object.write_date}</"
#~ "p>\n"
#~ "            <p><a href=\"${object.website_url}\">Presione aquí para abrir "
#~ "${object.slide_type}</a></p>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>\n"
#~ "<div style=\"padding:0px; margin:0px;\">\n"
#~ "<table cellpadding=\"0\" cellspacing=\"0\" style=\"margin: 10px 0px 0px "
#~ "0px; vertical-align: top;padding: 0px; font-family:arial; font-size:12px; "
#~ "color: #ffffff; background-color:#8f8f8f;\">\n"
#~ "    <tbody><tr>\n"
#~ "        <td style=\"width: 600px; font-size:12px; text-align:center; "
#~ "padding-top:10px; color:#a24689; padding-bottom:5px\">\n"
#~ "            <a href=\"${object.website_url}\" style=\"color:#a24689\">Ver "
#~ "en el Navegador</a> |\n"
#~ "            <a href=\"/page/contactus\" style=\"color:#a24689\">Contacto</"
#~ "a>\n"
#~ "        </td>\n"
#~ "    </tr></tbody>\n"
#~ "</table>\n"
#~ "</div>"

#~ msgid "Action Needed"
#~ msgstr "Necesaria acción"

#~ msgid ""
#~ "Create enterprise grade website with our super easy builder. Use finely "
#~ "designed building blocks and edit everything inline. Benefit from out-of-"
#~ "the-box business features; e-Commerce, events, blogs, jobs announces, "
#~ "customer references, call-to-actions, etc.\n"
#~ "\n"
#~ "This is a new open source application in Python/Javascript based on "
#~ "Bootstrap and OpenERP.\n"
#~ "\n"
#~ "Download or use online on http://openerp.com"
#~ msgstr ""
#~ "Cree una página web de nivel empresarial con nuestro súper fácil "
#~ "constructor de sitios. Utilice bloques de construcción finamente "
#~ "diseñados y edite todo en línea. Su compañía se beneficia de "
#~ "características de negocio avanzados; comercio electrónico, eventos, "
#~ "blogs, anuncios de trabajo, referencias de clientes, llamadas de acción, "
#~ "etc.\n"
#~ "\n"
#~ "Se trata de una nueva aplicación de código abierto en Python / Javascript "
#~ "basado en Bootstrap y Odoo.\n"
#~ "\n"
#~ "La puede descargar o utilizar en línea en http://odoo.com"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Empresas)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención."

#~ msgid ""
#~ "Interview with Guy Christiaens, Finance Manager at Alpinter, about their "
#~ "use of Odoo.\n"
#~ "Discover more about Odoo at www.odoo.com"
#~ msgstr ""
#~ "Entrevista con Guy Christiaens, Gerente financiero en Alpinter, sobre el "
#~ "uso de Odoo.\n"
#~ "Descubre más sobre Odoo en www.odoo.com"

#~ msgid "Is Follower"
#~ msgstr "Es un seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Odoo - The Voice of the Customer: Alpinter"
#~ msgstr "Odoo - La voz del consumidor: Alpinter"

#~ msgid "Odoo Experience - Register now https://www.odoo.com/event/304/"
#~ msgstr ""
#~ "Experiencia Odoo  - Registrese ahora https://www.odoo.com/event/304/"

#~ msgid "Odoo Open Days 2014"
#~ msgstr "Odoo Open Days 2014"

#~ msgid "Odoo POS Indiegogo Campaign"
#~ msgstr "Odoo POS Indiegogo Campaign"

#~ msgid "Odoo SEO - Boost Your Website Trafic"
#~ msgstr "SEO Odoo - Aumente su tráfico del sitio web"

#~ msgid "Open Source Website Builder and eCommerce: Odoo"
#~ msgstr "Constructor de Sitio Web y eCommerce Open Source: Odoo"

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través de la web."

#~ msgid ""
#~ "The open source OpenERP Promote tool suggests keywords according to "
#~ "Google most searched terms. Search Engine Optimization tools are ready to "
#~ "use, with no configuration required.\n"
#~ "\n"
#~ "Online demo and download: http://openerp.com"
#~ msgstr ""
#~ "El módulo de herramientas de palabras clave de OpenERP sugiere los "
#~ "términos de Google  más buscados. La herramientas de optimización de "
#~ "motores de búsqueda están listos para usarse, sin necesidad de "
#~ "configuración.\n"
#~ "\n"
#~ "Demostración en línea y descarga: http://openerp.com"

#~ msgid ""
#~ "The presentation video for our OpenSource Pos Hardware  campaign at "
#~ "indiegogo: \n"
#~ "\n"
#~ "https://www.indiegogo.com/projects/opensource-your-shop/"
#~ msgstr ""
#~ "El vídeo de presentación de nuestra campaña Hardware OpenSource Pos en "
#~ "IndieGoGo:\n"
#~ "\n"
#~ "https://www.indiegogo.com/projects/opensource-your-shop/"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin leer"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de mensajes no leidos"

#~ msgid "Visible in Website"
#~ msgstr "Visible en el sitio web"

#~ msgid "Website URL"
#~ msgstr "URL del sitio web"

#~ msgid "Website meta description"
#~ msgstr "Meta descripción del sitio web"

#~ msgid "Website meta keywords"
#~ msgstr "Meta palabras clave del sitio web"

#~ msgid "Website meta title"
#~ msgstr "Meta título del sitio web"
