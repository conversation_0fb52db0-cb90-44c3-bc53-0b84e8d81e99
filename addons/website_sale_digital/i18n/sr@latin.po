# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_digital
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: N<PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_sale_digital
#: model:product.template,description_sale:website_sale_digital.product_1
msgid ""
"Alice's Adventures in Wonderland (commonly shortened to Alice in Wonderland)"
" is an 1865 novel written by English author Charles Lutwidge Dodgson under "
"the pseudonym <PERSON> Carroll. It tells of a girl named Alice falling through "
"a rabbit hole into a fantasy world populated by peculiar, anthropomorphic "
"creatures. The tale plays with logic, giving the story lasting popularity "
"with adults as well as with children. It is considered to be one of the best"
" examples of the literary nonsense genre. Its narrative course and "
"structure, characters and imagery have been enormously influential in both "
"popular culture and literature, especially in the fantasy genre."
msgstr ""

#. module: website_sale_digital
#: model:product.template,name:website_sale_digital.product_1
msgid "Alice's Adventures in Wonderland - Lewis Caroll"
msgstr ""

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:23
#: code:addons/website_sale_digital/models/product.py:54
#, python-format
msgid "Digital Attachments"
msgstr ""

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_ir_attachment_product_downloadable
#: model:ir.model.fields,field_description:website_sale_digital.field_mrp_document_product_downloadable
msgid "Downloadable from product portal"
msgstr ""

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.portal_order_page_downloads
msgid "Downloads <span class=\"caret\"/>"
msgstr ""

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_product_product_attachment_count
#: model:ir.model.fields,field_description:website_sale_digital.field_product_template_attachment_count
msgid "File"
msgstr "Fajl"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka računa"

#. module: website_sale_digital
#: model:product.template,description:website_sale_digital.product_1
msgid "Novel by Lewis Caroll."
msgstr ""

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_product_view_form_inherit_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_template_view_form_inherit_digital
msgid "Portal Files"
msgstr ""

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_product
msgid "Product"
msgstr "Proizvod"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_ir_attachment
msgid "ir.attachment"
msgstr "ir.attachment"
