# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_asset
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-15 18:15+0000\n"
"PO-Revision-Date: 2022-07-06 00:05+0200\n"
"Last-Translator: <PERSON>yl<PERSON><PERSON>c\n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.1\n"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr " (copie)"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid " (grouped)"
msgstr " (regroupé)"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__entry_count
msgid "# Asset Entries"
msgstr "# Entrées d’Actif"

#. module: om_account_asset
#: model:ir.actions.server,name:om_account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:om_account_asset.account_asset_cron
#: model:ir.cron,name:om_account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr "Immo du compte: générer des entrées d'Immo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid "Account Date"
msgstr "Date Compte"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Compte utilisé dans les écritures d'amortissement, pour diminuer la valeur "
"de l'actif."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Compte utilisé dans les entrées périodiques, pour enregistrer une partie de "
"l'amortissement comme une dépense."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_asset_id
msgid "Account used to record the purchase of the asset at its original price."
msgstr ""
"Compte utilisé pour enregistrer les achats des amortissements à son prix de "
"base."

#. module: om_account_asset
#. openerp-web
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "Écritures comptables en attente de vérification manuelle"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction
msgid "Action Needed"
msgstr "Action nécessaire"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__active
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__active
msgid "Active"
msgstr "Actif"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "Options supplémentaires"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Amount"
msgstr "Montant"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "Montant des lignes d'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_value
msgid "Amount of Installment Lines"
msgstr "Montant des lignes de versement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__account_analytic_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_analytic_id
msgid "Analytic Account"
msgstr "Compte analytique"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__asset_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "Actifs"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Compte d'actif"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "Catégorie d'actif"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "Durées d'actif à modifier"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_end_date
msgid "Asset End Date"
msgstr "Date de fin de l'actif"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__asset_method_time
msgid "Asset Method Time"
msgstr "Actif Méthode Temps"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__name
msgid "Asset Name"
msgstr "Nom de l'actif"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_start_date
msgid "Asset Start Date"
msgstr "Date de début de l’immo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__name
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__asset_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr "Type d'actif"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_list_normal_purchase
msgid "Asset Category"
msgstr "Type d'actif"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "Catégorie d'actif"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "Actif créé"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "Ligne d'amortissement des actifs"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""
"Immobilisation vendue ou éliminée. Saisie comptable en attente de validation."

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "Reconnaissance des immobilisations/revenus"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_form
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:om_account_asset.menu_action_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "Actifs"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_asset_report
#: model:ir.model,name:om_account_asset.model_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_graph
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_pivot
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "Analyse des actifs"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "Lignes d'amortissement des actifs"

#. module: om_account_asset
#: model:ir.ui.menu,name:om_account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Immobilisations et revenus"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Amortissements terminés"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Immobilisations à l'état brouillon et ouvert"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "Actif Brouillon"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "Actifs en cours d’exécution"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__open_asset
msgid "Auto-Confirm Assets"
msgstr "Auto-confirmation les Immobilisations"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__last_day_period
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__last_day_period
msgid "Based on Last Day of Purchase Period"
msgstr "Basé sur la période du dernier jour d’achat"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Annuler"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Category"
msgstr "Catégorie"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr "Catégorie d'immobilisation"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""
"Cochez cette case si vous souhaitez confirmer automatiquement les actifs de "
"cette catégorie lorsqu’ils sont créés par des factures."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr ""
"Vérifiez ceci si vous souhaitez regrouper les ecritures générées par "
"catégories."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"Choisissez la méthode à utiliser pour calculer le montant des lignes "
"d’amortissement.\n"
"  * Linéaire: Calculé sur la base de: Valeur brute / Nombre "
"d’amortissements\n"
"  * Dégressif: Calculé sur la base de: Valeur résiduelle * Facteur dégressif"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 "
"depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the "
"depreciations won't go beyond."
msgstr ""
"Choisissez la méthode à utiliser pour calculer les dates et le nombre "
"d’entrées.\n"
"  * Nombre d’entrées: Fixez le nombre d’entrées et le temps entre 2 "
"amortissements.\n"
"  * Date de fin: Choisissez le temps entre 2 amortissements et la date à "
"laquelle les amortissements n’iront pas au-delà."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""
"Choisissez la période pour laquelle vous souhaitez comptabiliser "
"automatiquement les lignes d'amortissement des immobilisations en cours"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__close
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__close
msgid "Close"
msgstr "Fermer"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Closed"
msgstr "Fermé"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__company_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "Société"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method
msgid "Computation Method"
msgstr "Méthode de calcul"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "Ressource de calcul"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "Calculer l'amortissement"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "Confirmer"

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Asset Moves"
msgstr "Déplacements de ressources créés"

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Revenue Moves"
msgstr "Déplacements de revenus créés"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Créé sur"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciated_value
msgid "Cumulative Depreciation"
msgstr "Amortissement cumulé"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__currency_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__currency_id
#, python-format
msgid "Currency"
msgstr "Devise"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Current"
msgstr "Actuel"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__amount
msgid "Current Depreciation"
msgstr "Amortissement actuel"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__date
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Date"
msgstr "Fecha"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr "Date de l’Immobilisation"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "Date d’achat"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "Date d'amortissement"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "Compte de revenus différé"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__deferred_revenue_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__deferred_revenue_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "Type de revenus reportés"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "Revenus différés"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__degressive
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__degressive
msgid "Degressive"
msgstr "Dégressive"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_progress_factor
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_progress_factor
msgid "Degressive Factor"
msgstr "Facteur dégressif"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "Amortissement"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "Commission d'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_nbr
msgid "Depreciation Count"
msgstr "Compte d'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciation_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_date
msgid "Depreciation Date"
msgstr "Date d'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date_first_depreciation
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__date_first_depreciation
msgid "Depreciation Dates"
msgstr "Dates d'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "Écritures d'amortissement : compte d'immobilisations"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "Écritures d'amortissement : compte de dépenses"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_id
msgid "Depreciation Entry"
msgstr "Saisie d'amortissement"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "Informations sur l'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__depreciation_line_ids
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "Lignes d'amortissement"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "Méthode d'amortissement"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "Mois d'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__name
msgid "Depreciation Name"
msgstr "Nom de l'amortissement"

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "Tableau des amortissements modifié"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Depreciation line posted."
msgstr "Ligne d'amortissement comptabilisée."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Afficher un nom"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "Écriture d'élimination"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr "Écritures d'élimination"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Document closed."
msgstr "Document fermé."

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__draft
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__draft
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "Brouillon"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__end
msgid "Ending Date"
msgstr "Date de fin"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_end
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_end
msgid "Ending date"
msgstr "Date de fin"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "Filtres étendus..."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid "First Depreciation Date"
msgstr "Première date d'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_follower_ids
msgid "Followers"
msgstr "Suiveuses"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets "
"depreciation reporting."
msgstr ""
"À partir de ce rapport, vous pouvez avoir une vue d’ensemble de toutes les "
"dépréciations.\n"
"            la barre de recherche peut également être utilisée pour "
"personnaliser la déclaration d’amortissement de vos actifs."

#. module: om_account_asset
#: model:ir.ui.menu,name:om_account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "Générer des entrées d’actifs"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "Générer les lignes d'écritures"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__gross_value
msgid "Gross Amount"
msgstr "Montant HT"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value
msgid "Gross Value"
msgstr "Valeur brute"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "Valeur brute de l'actif"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "Grouper par"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Regrouper par..."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__group_entries
msgid "Group Journal Entries"
msgstr "Grouper les Pièces comptables"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__has_message
msgid "Has Message"
msgstr "A un message"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the asset date (purchase date) instead of the first January / Start "
"date of fiscal year"
msgstr ""
"Indique que la première écriture d'amortissement pour cet actif doit être "
"effectuée à partir de la date de l'actif (date d'achat) au lieu du premier "
"janvier / date de début de l'exercice"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""
"Indique que la première écriture d'amortissement pour ce bien doit être "
"effectuée à partir de la date d'achat au lieu du premier janvier"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_nbr
msgid "Installment Count"
msgstr "Nombre de versements"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__invoice_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "Facture"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Il s'agit de la part non dépréciable de l'immobilisation."

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Items"
msgstr "Éléments"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__journal_id
msgid "Journal"
msgstr "Journal"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "Pièces comptables"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__linear
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__linear
msgid "Linear"
msgstr "Linéaire"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_check
msgid "Linked"
msgstr "Lié"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__manual
msgid "Manual"
msgstr "Manuel"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__manual
msgid "Manual (Defaulted on Purchase Date)"
msgstr "Manuel (par défaut à la date d’achat)"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_ids
msgid "Messages"
msgstr "Messages"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify"
msgstr "Modifier"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_modify
#: model:ir.model,name:om_account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Modifier l'actif"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "Modifier l'amortissement"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "Revenus récurrents mensuels"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__remaining_value
msgid "Next Period Depreciation"
msgstr "Amortissement de la prochaine période"

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid "No content"
msgstr "Aucun contenu"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__note
msgid "Note"
msgstr "Note"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"Notez que cette date ne modifie pas le calcul de la première entrée de "
"journal dans le cas d'actifs prorata temporis. Il change simplement sa date "
"de comptabilisation"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_number
msgid "Number of Depreciations"
msgstr "Nombre d'amortissements"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__number
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__number
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "Nombre d’entrées"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_period
msgid "Number of Months in a Period"
msgstr "Nombre de mois dans une période"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr "Une entrée par an"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__partner_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__partner_id
#, python-format
msgid "Partner"
msgstr "Partenaire"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_period
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_period
msgid "Period Length"
msgstr "Durée de la période"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "Périodicité"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "Lignes après amortissement"

#. module: om_account_asset
#. openerp-web
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_posted_check
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__move_check
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "Comptabilisé"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__posted_value
msgid "Posted Amount"
msgstr "Montant comptabilisé"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "Lignes d'amortissement comptabilisées"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_product_template
msgid "Product Template"
msgstr "Modèle d'article"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__prorata
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__prorata
msgid "Prorata Temporis"
msgstr "Prorata temporis"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid ""
"Prorata temporis can be applied only for the \"number of depreciations\" "
"time method."
msgstr ""
"Le prorata temporis ne peut être appliqué que pour la méthode du temps du "
"« nombre d’amortissements »."

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "Achats"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "Mois d’achat"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__purchase
msgid "Purchase: Asset"
msgstr "Achat : Immobilisation"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__name
msgid "Reason"
msgstr "Motif"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr "Compte de reconnaissance"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "Compte de comptabilisation des produits"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__code
msgid "Reference"
msgstr "Référence"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "Résiduel"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value_residual
msgid "Residual Value"
msgstr "Valeur résiduelle"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__open
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__open
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "En cours"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__sale
msgid "Sale: Revenue Recognition"
msgstr "Ventes : reconnaissance de revenu"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "Vente"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__salvage_value
msgid "Salvage Value"
msgstr "Valeur de récupération"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "recherche de Catégorie d’Immobilisation"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "Vendre ou éliminer"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "Marquer comme brouillon"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "Définir ici le temps entre 2 dépréciations, en mois"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid "State of Asset"
msgstr "État de l’actif"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__state
msgid "Status"
msgstr "Statut"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_period
msgid "The amount of time between two depreciations, in months"
msgstr "Le temps entre deux amortissements, en mois"

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"The number of depreciations must be greater than the number of posted or "
"draft entries to allow for complete depreciation of the asset."
msgstr ""
"Le nombre d’amortissements doit être supérieur au nombre d’écritures "
"affichées ou provisoires pour permettre l’amortissement complet de l’actif."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr ""
"Le nombre d'amortissements nécessaire pour amortir votre immobilisation"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be 0."
msgstr ""
"Le nombre d’amortissements ou la durée de votre catégorie d’actifs ne peut "
"pas être égal à 0."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be "
"based on the last day of the purchase month or the purchase year (depending "
"on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the "
"purchase date."
msgstr ""
"La façon de calculer la date du premier amortissement.\n"
"  * Basé sur le dernier jour de la période d’achat: Les dates "
"d’amortissement seront basées sur le dernier jour du mois d’achat ou de "
"l’année d’achat (selon la périodicité des amortissements).\n"
"  * Basé sur la date d’achat: Les dates d’amortissement seront basées sur la "
"date d’achat."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be "
"based on the last day of the purchase month or the purchase year (depending "
"on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the "
"purchase date.\n"
msgstr ""
"La façon de calculer la date de la première dépréciation.\n"
"  * Selon le dernier jour de la période d’achat : Les dates d’amortissement "
"seront basées sur le dernier jour du mois d’achat ou de l’année d’achat "
"(selon la périodicité des amortissements).\n"
"  * Selon la date d’achat : Les dates d’amortissement seront basées sur la "
"date d’achat.\n"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid ""
"This depreciation is already linked to a journal entry. Please post or "
"delete it."
msgstr ""
"Cette dépréciation est déjà liée à une entrée de journal. Veuillez le "
"publier ou le supprimer."

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month."
"<br/>\n"
"                        This will generate journal entries for all related "
"installment lines on this period\n"
"                        of asset/revenue recognition as well."
msgstr ""
"Cet assistant publiera les lignes de versement/amortissement pour le mois "
"sélectionné.<br/>Cela générera des entrées de journal pour toutes les lignes "
"de versements connexes sur cette période\n"
"                        de la comptabilisation des actifs et des produits "
"également."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_time
msgid "Time Method"
msgstr "Méthode du temps"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "Méthode de temps basée sur"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__type
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__type
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Type"
msgstr "Type"

#. module: om_account_asset
#. openerp-web
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#, python-format
msgid "Unposted"
msgstr "Non Validé"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__unposted_value
msgid "Unposted Amount"
msgstr "Montant non validé"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Fournisseur"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid "Vendor bill cancelled."
msgstr "Facture fournisseur annulée."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,help:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation "
"lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last "
"line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Lorsqu’une ressource est créée, l’état est « Brouillon ».\n"
"Si l’actif est confirmé, l’état passe en « En cours » et les lignes "
"d’amortissement peuvent être enregistrées dans la comptabilité.\n"
"Vous pouvez fermer manuellement un actif lorsque l’amortissement est "
"terminé. Si la dernière ligne d’amortissement est comptabilisée, l’actif "
"passe automatiquement dans cet état."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__name
msgid "Year"
msgstr "Année"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr ""
"Vous ne pouvez pas supprimer un document qui contient des entrées validées."

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr ""
"Vous ne pouvez pas supprimer un document qui se trouve dans l'étape %s."

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "Vous ne pouvez pas supprimer les lignes d'amortissement validées."

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "Vous ne pouvez pas supprimer les lignes de versement publiées."

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft for an entry having a posted asset"
msgstr ""
"Vous ne pouvez pas réinitialiser le brouillon pour une entrée ayant une "
"ressource validée"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr "asset.depreciation.confirmation.wizard"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "p. ex. Ordinateurs"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "ex : ordinateur portable iBook"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "months"
msgstr "mois"
