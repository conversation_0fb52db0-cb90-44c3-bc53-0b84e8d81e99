<?xml version="1.0" ?>
<odoo>
    <record model="ir.ui.view" id="hr_holidays_inherit_form_view1">
        <field name="name">hr.holidays.form.view1</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form_manager"/>
        <field name="arch" type="xml">
            <field name="department_id" position="after">
                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_attendance_inherit_form_view1">
        <field name="name">hr.attendance.form.view1</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_form"/>
        <field name="arch" type="xml">
            <field name="check_out" position="after">
                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_payslip_batch_inherit_form_view1">
        <field name="name">hr.payslip.run.form.view1</field>
        <field name="model">hr.payslip.run</field>
        <field name="inherit_id" ref="hr_payroll_community.hr_payslip_run_form"/>
        <field name="arch" type="xml">
            <field name="credit_note" position="after">
                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_salary_category_inherit_form_view1">
        <field name="name">hr.salary.rule.category.form.view1</field>
        <field name="model">hr.salary.rule.category</field>
        <field name="inherit_id" ref="hr_payroll_community.hr_salary_rule_category_form"/>
        <field name="arch" type="xml">
            <field name="code" position="after">
                <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
            </field>
        </field>
    </record>
</odoo>
