from odoo import api, fields, models


class DeferredExpensesPost(models.TransientModel):
    _name = 'deferred.expense.post'
    _description = 'Deferred Expenses Post'
    date = fields.Date(
        string='Date',
        required=False)

    def action_journal_entry_posted(self):
        entries = self.env['account.asset'].search([('asset_type', '=', 'expense')]).mapped(
            'depreciation_move_ids')
        for entry in entries:
            today = fields.Date.today()
            if today >= entry.date and entry.state == 'draft':
                entry.action_post()
