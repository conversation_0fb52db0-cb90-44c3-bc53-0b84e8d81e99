<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
		<!-- view common to both template and product -->
        <record id="view_template_branch_form" model="ir.ui.view">
            <field name="name">product.template.branch.form.inherit</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
					<field name="cost_currency_id" position="before">
						<field name="branch_id"/>
					</field>
			</field>
		</record>
    </data>
</odoo>